{"name": "trellone", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "prettier --check \"src/**/(*.tsx|*.ts|*.css|*.scss)\"", "prettier:fix": "prettier --write \"src/**/(*.tsx|*.ts|*.css|*.scss)\""}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.10.0", "@mui/icons-material": "^5.16.14", "@mui/lab": "^5.0.0-alpha.163", "@mui/material": "^5.16.14", "@mui/x-date-pickers": "^6.9.2", "@reduxjs/toolkit": "^2.6.0", "@uidotdev/usehooks": "^2.4.1", "@uiw/react-md-editor": "^4.0.5", "axios": "^1.8.3", "date-fns": "^2.30.0", "emoji-picker-react": "^4.12.3", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "material-ui-confirm": "^4.0.0", "mui-color-input": "^1.1.1", "randomcolor": "^0.6.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "react-infinite-scroll-component": "^6.1.0", "react-redux": "^9.2.0", "react-router-dom": "^6.29.0", "react-toastify": "^11.0.5", "redux-persist": "^6.0.0", "rehype-sanitize": "^6.0.0", "socket.io-client": "^4.8.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/lodash": "^4.17.16", "@types/node": "^22.13.5", "@types/randomcolor": "^0.5.9", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "prettier": "^3.5.2", "rollup-plugin-visualizer": "^5.14.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-plugin-svgr": "^4.3.0"}}