import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import OpenInNewIcon from '@mui/icons-material/OpenInNew'
import Box from '@mui/material/Box'
import Divider from '@mui/material/Divider'
import { Link as MuiLink } from '@mui/material'
import List from '@mui/material/List'
import ListItem from '@mui/material/ListItem'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import Paper from '@mui/material/Paper'
import Popper from '@mui/material/Popper'
import Typography from '@mui/material/Typography'
import { toast } from 'react-toastify'
import Favicon from '~/components/Favicon'
import { getDomainFromUrl } from '~/utils/url'

interface LinkPreviewData {
  url: string
  title: string
  description: string
  thumbnail?: string
  domain: string
}

interface LinkPreviewPopupProps {
  open: boolean
  anchorEl: HTMLElement | null
  linkData: LinkPreviewData | null
  onMouseEnter: () => void
  onMouseLeave: () => void
}

// Mock data for different domains
const getMockLinkData = (url: string): LinkPreviewData => {
  const domain = getDomainFromUrl(url)

  const mockData: Record<string, Omit<LinkPreviewData, 'url' | 'domain'>> = {
    'github.com': {
      title: 'GitHub - The complete developer platform',
      description:
        'GitHub is where over 100 million developers shape the future of software, together. Contribute to the open source community, manage Git repositories, and build software alongside 100 million developers.',
      thumbnail: 'https://github.githubassets.com/images/modules/site/social-cards/github-social.png'
    },
    'stackoverflow.com': {
      title: 'Stack Overflow - Where Developers Learn, Share, & Build Careers',
      description:
        'Stack Overflow is the largest, most trusted online community for developers to learn, share their programming knowledge, and build their careers.',
      thumbnail: 'https://cdn.sstatic.net/Sites/stackoverflow/Img/<EMAIL>'
    },
    'youtube.com': {
      title: 'YouTube',
      description:
        'Enjoy the videos and music you love, upload original content, and share it all with friends, family, and the world on YouTube.',
      thumbnail: 'https://www.youtube.com/img/desktop/yt_1200.png'
    },
    'medium.com': {
      title: 'Medium – Where good ideas find you',
      description:
        'Medium is an open platform where readers find dynamic thinking, and where expert and undiscovered voices can share their writing on any topic.',
      thumbnail: 'https://miro.medium.com/v2/resize:fit:1200/1*jfdwtvU6V6g99q3G7gq7dQ.png'
    },
    'figma.com': {
      title: 'Figma: The collaborative interface design tool',
      description:
        'Figma is a collaborative web application for interface design, with additional offline features enabled by desktop applications for macOS and Windows.',
      thumbnail:
        'https://cdn.sanity.io/images/599r6htc/localized/46a76c802176eb17b04e12108de7e7e0f3736dc6-1024x1024.png'
    },
    'canva.com': {
      title: 'Canva: Visual Suite for Everyone',
      description:
        'Canva is a free-to-use online graphic design tool. Use it to create social media posts, presentations, posters, videos, logos and more.',
      thumbnail: 'https://static-cse.canva.com/blob/825910/canvasocial.jpg'
    },
    'notion.so': {
      title: 'Notion – The all-in-one workspace for your notes, tasks, wikis, and databases',
      description:
        "A new tool that blends your everyday work apps into one. It's the all-in-one workspace for you and your team.",
      thumbnail: 'https://www.notion.so/images/meta/default.png'
    },
    'trello.com': {
      title: 'Trello | Manage Team Projects & Tasks',
      description:
        'Trello is the visual collaboration platform that gives teams perspective on projects. Use Trello to collaborate, communicate and coordinate on all of your projects.',
      thumbnail: 'https://d2k1ftgv7pobq7.cloudfront.net/meta/c/p/res/images/trello-meta-logo.png'
    },
    'slack.com': {
      title: 'Slack is your productivity platform',
      description:
        "Slack is a new way to communicate with your team. It's faster, better organized, and more secure than email.",
      thumbnail: 'https://a.slack-edge.com/80588/marketing/img/meta/slack_hash_256.png'
    },
    'discord.com': {
      title: 'Discord | Your Place to Talk and Hang Out',
      description:
        'Discord is the easiest way to talk over voice, video, and text. Talk, chat, hang out, and stay close with your friends and communities.',
      thumbnail: 'https://discord.com/assets/652f40427ed5ddb4c719.png'
    },
    'linkedin.com': {
      title: 'LinkedIn: Log In or Sign Up',
      description:
        "LinkedIn is the world's largest professional network with 900+ million members in more than 200 countries and territories worldwide.",
      thumbnail: 'https://static.licdn.com/aero-v1/sc/h/al2o9zrvru7aqj8e1x2rzsrca'
    },
    'twitter.com': {
      title: "X. It's what's happening",
      description:
        'From breaking news and entertainment to sports and politics, get the full story with all the live commentary.',
      thumbnail: 'https://abs.twimg.com/responsive-web/client-web/icon-ios.b1fc7275.png'
    },
    'x.com': {
      title: "X. It's what's happening",
      description:
        'From breaking news and entertainment to sports and politics, get the full story with all the live commentary.',
      thumbnail: 'https://abs.twimg.com/responsive-web/client-web/icon-ios.b1fc7275.png'
    },
    'google.com': {
      title: 'Google',
      description:
        "Search the world's information, including webpages, images, videos and more. Google has many special features to help you find exactly what you're looking for.",
      thumbnail: 'https://www.google.com/images/branding/googleg/1x/googleg_standard_color_128dp.png'
    },
    'docs.google.com': {
      title: 'Google Docs',
      description:
        'Create and edit web-based documents, spreadsheets, and presentations. Store documents online and access them from any computer.',
      thumbnail: 'https://ssl.gstatic.com/docs/documents/images/kix-favicon7.ico'
    },
    'drive.google.com': {
      title: 'Google Drive',
      description:
        'Access Google Drive with a free Google account (for personal use) or Google Workspace account (for business use).',
      thumbnail: 'https://ssl.gstatic.com/images/branding/product/1x/drive_2020q4_32dp.png'
    }
  }

  const defaultData = {
    title: 'Link Preview',
    description: 'Click to open this link in a new tab',
    thumbnail: undefined
  }

  return {
    url,
    domain,
    ...(mockData[domain] || defaultData)
  }
}

export default function LinkPreviewPopup({
  open,
  anchorEl,
  linkData,
  onMouseEnter,
  onMouseLeave
}: LinkPreviewPopupProps) {
  const handleCopyLink = async () => {
    if (!linkData) return

    try {
      await navigator.clipboard.writeText(linkData.url)
      toast.success('Link copied to clipboard!', { position: 'top-center' })
    } catch (error) {
      toast.error('Failed to copy link', { position: 'top-center' })
      console.error('Error copying link:', error)
    }
  }

  const handleOpenLink = () => {
    if (!linkData) return
    window.open(linkData.url, '_blank', 'noopener,noreferrer')
  }

  if (!linkData) return null

  const mockData = getMockLinkData(linkData.url)

  return (
    <Popper
      open={open}
      anchorEl={anchorEl}
      placement='bottom-start'
      modifiers={[
        {
          name: 'offset',
          options: { offset: [0, 8] }
        },
        {
          name: 'preventOverflow',
          options: {
            boundary: 'viewport',
            padding: 8
          }
        }
      ]}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      sx={{ zIndex: 1400 }}
    >
      <Paper
        elevation={12}
        sx={{
          width: 320,
          borderRadius: 2,
          overflow: 'hidden',
          bgcolor: (theme) => (theme.palette.mode === 'dark' ? '#2a2d3a' : '#ffffff'),
          border: (theme) => `1px solid ${theme.palette.mode === 'dark' ? '#404040' : '#e0e0e0'}`,
          boxShadow: (theme) =>
            theme.palette.mode === 'dark' ? '0 8px 32px rgba(0, 0, 0, 0.4)' : '0 8px 32px rgba(0, 0, 0, 0.12)'
        }}
      >
        {/* Thumbnail Section */}
        {mockData.thumbnail && (
          <Box
            sx={{
              height: 160,
              backgroundImage: `url(${mockData.thumbnail})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
              position: 'relative',
              cursor: 'pointer'
            }}
            onClick={handleOpenLink}
          >
            <Box
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                bgcolor: 'rgba(0, 0, 0, 0.6)',
                borderRadius: 1,
                p: 0.5,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <OpenInNewIcon sx={{ fontSize: 16, color: 'white' }} />
            </Box>
          </Box>
        )}

        {/* Content Section */}
        <Box sx={{ p: 2 }}>
          <MuiLink
            href={mockData.url}
            target='_blank'
            rel='noopener noreferrer'
            sx={{
              display: 'block',
              color: (theme) => (theme.palette.mode === 'dark' ? '#4fc3f7' : '#1976d2'),
              textDecoration: 'none',
              fontWeight: 600,
              fontSize: '15px',
              lineHeight: 1.4,
              mb: 1,
              '&:hover': {
                textDecoration: 'underline'
              }
            }}
          >
            {mockData.title}
          </MuiLink>

          <Typography
            variant='body2'
            sx={{
              color: (theme) => (theme.palette.mode === 'dark' ? '#b0b0b0' : '#666666'),
              fontSize: '13px',
              lineHeight: 1.4,
              mb: 1.5,
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }}
          >
            {mockData.description}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Favicon url={mockData.url} size={16} />
            <Typography
              variant='caption'
              sx={{
                color: (theme) => (theme.palette.mode === 'dark' ? '#b0b0b0' : '#666666'),
                fontSize: '12px'
              }}
            >
              {mockData.domain}
            </Typography>
          </Box>
        </Box>

        <Divider />

        {/* Actions Section */}
        <List disablePadding>
          <ListItem disablePadding>
            <ListItemButton
              onClick={handleOpenLink}
              sx={{
                py: 1,
                px: 2,
                '&:hover': {
                  bgcolor: (theme) => (theme.palette.mode === 'dark' ? '#3a3d4a' : '#f5f5f5')
                }
              }}
            >
              <ListItemIcon sx={{ minWidth: 24 }}>
                <OpenInNewIcon
                  sx={{
                    fontSize: 16,
                    color: (theme) => (theme.palette.mode === 'dark' ? '#b0b0b0' : '#666666')
                  }}
                />
              </ListItemIcon>
              <ListItemText
                primary='Open preview'
                primaryTypographyProps={{
                  fontSize: '13px',
                  color: (theme) => (theme.palette.mode === 'dark' ? '#ffffff' : '#000000')
                }}
              />
            </ListItemButton>
          </ListItem>

          <ListItem disablePadding>
            <ListItemButton
              onClick={handleCopyLink}
              sx={{
                py: 1,
                px: 2,
                '&:hover': {
                  bgcolor: (theme) => (theme.palette.mode === 'dark' ? '#3a3d4a' : '#f5f5f5')
                }
              }}
            >
              <ListItemIcon sx={{ minWidth: 24 }}>
                <ContentCopyIcon
                  sx={{
                    fontSize: 16,
                    color: (theme) => (theme.palette.mode === 'dark' ? '#b0b0b0' : '#666666')
                  }}
                />
              </ListItemIcon>
              <ListItemText
                primary='Copy link'
                primaryTypographyProps={{
                  fontSize: '13px',
                  color: (theme) => (theme.palette.mode === 'dark' ? '#ffffff' : '#000000')
                }}
              />
            </ListItemButton>
          </ListItem>
        </List>
      </Paper>
    </Popper>
  )
}

export type { LinkPreviewData }
