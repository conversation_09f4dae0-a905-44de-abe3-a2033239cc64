---
description: Project directory structure
globs:
alwaysApply: true
---
# Project Directory Structure

Complete guide to the Trellone project's file organization and important directories.

## Root Level Files

- `package.json` - Node.js dependencies and scripts
- `package-lock.json` - Exact dependency versions
- `tsconfig.json` - TypeScript configuration
- `tsconfig.app.json` - App-specific TypeScript config
- `tsconfig.node.json` - Node-specific TypeScript config
- `vite.config.ts` - Vite build tool configuration
- `eslint.config.js` - ESLint linting rules
- `index.html` - Main HTML entry point
- `vercel.json` - Vercel deployment configuration
- `README.md` - Project documentation

## Core Source Structure (`src/`)

### Main Application Files
- `src/main.tsx` - React application entry point
- `src/App.tsx` - Root React component
- `src/index.css` - Global styles
- `src/theme.ts` - Material-UI theme configuration
- `src/vite-env.d.ts` - Vite environment types

### Components (`src/components/`)
Reusable UI components organized by feature:

**Navigation & Layout:**
- `AppBar/` - Main application bar
- `NavBar/` - Navigation bar with search, create, profiles
- `DrawerHeader/` - Drawer header component
- `Main/` - Main layout wrapper

**UI Components:**
- `Form/` - Form-related components (TextFieldInput, ToggleFocusInput, etc.)
- `Dialog/` - Modal dialogs (NewBoardDialog)
- `Modal/` - Complex modals (ActiveCard with attachments, dates, etc.)
- `Loading/` - Loading indicators
- `ErrorBoundary/` - Error handling component
- `Workspace/` - Workspace-related UI components

### Pages (`src/pages/`)
Route-specific page components:

**Authentication:**
- `Auth/` - Login, Register, OAuth, password reset
- `Auth/layouts/AuthLayout/` - Auth page layout

**Main Application:**
- `Boards/` - Board management and details
- `Workspaces/` - Workspace home and board lists
- `Settings/` - User settings and account management
- `404/` - Error pages

**Boards Feature (`src/pages/Boards/`):**
- `BoardDetails/` - Main board view
- `BoardDetails/components/` - Board-specific components (BoardBar, BoardContent, Card, Column, etc.)
- `BoardInvitationVerification/` - Board invitation handling

**Workspaces Feature (`src/pages/Workspaces/`):**
- `Home/` - Workspace dashboard
- `BoardsList/` - List of workspace boards
- `layouts/HomeLayout/` - Workspace layout
- `components/NavigationMenu/` - Workspace navigation

### Business Logic (`src/`)

**State Management:**
- `store/` - Redux store configuration
- `store/slices/` - Redux slices (app, auth, board, card, notification)
- `store/root.reducer.ts` - Root reducer combining all slices

**API & Data:**
- `queries/` - React Query hooks for API calls
- `schemas/` - Zod validation schemas
- `types/` - TypeScript type definitions

**Utilities:**
- `lib/` - Core utilities (HTTP client, JWT, Redux, Socket.io, sensors)
- `utils/` - Helper functions (formatters, validators, error handlers, etc.)
- `hooks/` - Custom React hooks
- `constants/` - Application constants and configuration

### Assets (`src/assets/`)
Static assets organized by feature:
- `auth/` - Authentication-related images
- `404/` - Error page assets
- `react.svg`, `trello.svg` - App icons

## Public Assets (`public/`)
- `board.svg`, `vite.svg` - Public static assets

## File Organization Principles

### Component Structure
Each component follows this pattern:
```
ComponentName/
├── ComponentName.tsx    # Main component implementation
└── index.ts            # Export barrel file
```

### Page Structure
Pages are organized by feature with optional nested components:
```
PageName/
├── PageName.tsx        # Main page component
├── index.ts           # Export barrel file
└── components/        # Page-specific components
    └── SubComponent/
        ├── SubComponent.tsx
        └── index.ts
```

### Business Logic Organization
- **Queries**: API calls grouped by entity (auth, boards, cards, etc.)
- **Schemas**: Validation schemas matching API entities
- **Types**: TypeScript definitions for data structures
- **Store**: Redux state management split by domain

## Key Architectural Patterns

1. **Feature-based organization** - Components and pages grouped by business domain
2. **Barrel exports** - Each directory has an index.ts for clean imports
3. **Separation of concerns** - UI, business logic, and data access clearly separated
4. **Consistent naming** - PascalCase for components, kebab-case for files
5. **Modular structure** - Each feature can be developed independently

## Import Patterns

```typescript
// Component imports
import { ComponentName } from '@/components/ComponentName'

// Page imports  
import { PageName } from '@/pages/Feature/PageName'

// Utility imports
import { utilFunction } from '@/utils/file-name'

// Query imports
import { useEntityQuery } from '@/queries/entity'
```

## Guidelines

1. **New components** should follow the established directory structure
2. **Feature additions** should be grouped logically with related functionality
3. **Shared utilities** belong in `/utils` or `/lib`
4. **API-related code** belongs in `/queries` and `/schemas`
5. **State management** should use the existing Redux slice pattern
