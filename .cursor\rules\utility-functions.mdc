---
description: Utility functions conventions
globs:
alwaysApply: true
---
# Utility Functions Best Practices

This rule defines the comprehensive patterns and conventions for utility functions in the Trellone project, based on the established patterns in [src/utils](mdc:src/utils).

## Directory Structure and Organization

All utility functions are organized in [src/utils/](mdc:src/utils) with specific files for different types of utilities:

- [utils.ts](mdc:src/utils/utils.ts) - General utility functions (color generation, placeholders, DOM manipulation)
- [validators.ts](mdc:src/utils/validators.ts) - File and data validation functions
- [storage.ts](mdc:src/utils/storage.ts) - Local storage management utilities
- [error-handlers.ts](mdc:src/utils/error-handlers.ts) - Error type checking and handling functions
- [oauth.ts](mdc:src/utils/oauth.ts) - OAuth and authentication utilities
- [formatters.ts](mdc:src/utils/formatters.ts) - String and data formatting functions
- [sorts.ts](mdc:src/utils/sorts.ts) - Array sorting and ordering utilities

## Function Naming Conventions

### Verb-based Action Names
```typescript
// ✅ Good - Clear action verbs
export const generateColorFromString = (string: string) => { ... }
export const generatePlaceholderCard = (column: ColumnType) => { ... }
export const interceptorLoadingElements = (calling: boolean) => { ... }
export const capitalizeFirstLetter = (value: string) => { ... }

// ❌ Bad - Vague or unclear names
export const colorFromString = (string: string) => { ... }
export const placeholder = (column: ColumnType) => { ... }
```

### Validator Function Patterns
```typescript
// ✅ Good - Validator naming with descriptive suffixes
export const singleFileValidator = (file: File) => { ... }
export const multipleDocumentFilesValidator = (files: FileList) => { ... }

// Return null for valid, string for invalid
const errorMessage = singleFileValidator(file)
if (errorMessage) {
  // Handle validation error
}
```

### Storage Function Patterns
```typescript
// ✅ Good - Clear storage action naming
export const setAccessTokenToLS = (access_token: string) => { ... }
export const getAccessTokenFromLS = () => { ... }
export const clearLS = () => { ... }

// Use consistent naming: action + data + ToLS/FromLS suffix
```

### Type Guard Function Patterns
```typescript
// ✅ Good - Type guard naming with 'is' prefix
export function isAxiosError<T>(error: unknown): error is AxiosError<T> { ... }
export function isAxiosUnauthorizedError<T>(error: unknown): error is AxiosError<T> { ... }
export function isFetchBaseQueryError(error: unknown): error is FetchBaseQueryError { ... }
```

## Function Signature Patterns

### Parameter Validation and Safety
```typescript
// ✅ Good - Null safety and defensive programming
export const mapOrder = <T extends Record<K, V>, K extends string | number | symbol, V>(
  originalArray: T[] | undefined | null,
  orderArray: V[] | undefined | null,
  key: K
): T[] => {
  if (!originalArray || !orderArray || !key) return []
  // Implementation...
}

// ✅ Good - Handle edge cases in validators
export const multipleDocumentFilesValidator = (files: FileList) => {
  if (!files || files.length === 0) {
    return null  // Valid case - no files to validate
  }
  // Validation logic...
}
```

### TypeScript Generic Patterns
```typescript
// ✅ Good - Comprehensive generic typing
export const mapOrder = <T extends Record<K, V>, K extends string | number | symbol, V>(
  originalArray: T[] | undefined | null,
  orderArray: V[] | undefined | null,
  key: K
): T[] => {
  // Implementation with type safety
}

// ✅ Good - Generic type guards
export function isAxiosError<T>(error: unknown): error is AxiosError<T> {
  return axios.isAxiosError(error)
}
```

## Validation Patterns

### File Validation Standards
```typescript
// ✅ Good - Comprehensive file validation
export const singleFileValidator = (file: File) => {
  if (file && (file.size >= config.maxSizeUploadAvatar || !file.type.includes('image'))) {
    return 'Maximum file size is 3MB and file type must be an image.'
  }
  return null
}

// ✅ Good - Multiple file validation with detailed errors
export const multipleDocumentFilesValidator = (files: FileList) => {
  // Check maximum number of files
  if (files.length > config.maxFileUploadDocument) {
    return `You can upload a maximum of ${config.maxFileUploadDocument} files.`
  }

  let totalSize = 0
  for (const file of files) {
    // Individual file size check
    if (file.size > config.maxSizeUploadDocument) {
      return `File "${file.name}" exceeds the maximum size of ${config.maxSizeUploadDocument / (1024 * 1024)}MB.`
    }

    // File type check
    const allowedMimeTypes = [...config.allowedDocumentTypes, ...config.allowedImageMimeTypes]
    if (!allowedMimeTypes.includes(file.type)) {
      return `File type of "${file.name}" is not allowed.`
    }

    totalSize += file.size
  }

  // Total size check
  if (totalSize > config.maxSizeUploadDocumentTotal) {
    return `Total upload size exceeds the maximum of ${config.maxSizeUploadDocumentTotal / (1024 * 1024)}MB.`
  }

  return null
}
```

**Validation Conventions:**
- Return `null` for valid cases, descriptive `string` for invalid cases
- Use configuration constants from [config.ts](mdc:src/constants/config.ts)
- Provide specific error messages with file names and size limits
- Check individual and total constraints for multiple files
- Include file type validation using MIME types

## Error Handling Patterns

### Type Guard Functions for Error Detection
```typescript
// ✅ Good - Axios error type guards
export function isAxiosError<T>(error: unknown): error is AxiosError<T> {
  return axios.isAxiosError(error)
}

export function isAxiosUnauthorizedError<T>(error: unknown): error is AxiosError<T> {
  return isAxiosError(error) && error.response?.status === HttpStatusCode.Unauthorized
}

export function isAxiosExpiredTokenError<T>(error: unknown): error is AxiosError<T> {
  return isAxiosUnauthorizedError<{ message: string }>(error) && 
         error.response?.data?.message === 'Jwt expired'
}

// ✅ Good - RTK Query error type guards
export function isFetchBaseQueryError(error: unknown): error is FetchBaseQueryError {
  return typeof error === 'object' && error !== null && 'status' in error
}

export function isUnprocessableEntityError<T>(error: unknown): error is EntityError<T> {
  return (
    isFetchBaseQueryError(error) &&
    error.status === 422 &&
    typeof error.data === 'object' &&
    error.data !== null &&
    !(error.data instanceof Array)
  )
}
```

**Error Handling Conventions:**
- Use specific type guards for different error types
- Build complex type guards by composing simpler ones
- Use HTTP status codes from [constants](mdc:src/constants/http-status-code.ts)
- Handle both Axios and RTK Query error types
- Include generic typing for error data structures

## Storage Utility Patterns

### Local Storage Management
```typescript
// ✅ Good - Storage utilities with event handling
export const LocalStorageEventTarget = new EventTarget()

export const setAccessTokenToLS = (access_token: string) => {
  localStorage.setItem('access_token', access_token)
}

export const clearLS = () => {
  localStorage.removeItem('access_token')
  localStorage.removeItem('refresh_token')
  localStorage.removeItem('profile')

  const clearLSEvent = new Event('clearLS')
  LocalStorageEventTarget.dispatchEvent(clearLSEvent)
}

export const getAccessTokenFromLS = () => localStorage.getItem('access_token') || ''
```

**Storage Conventions:**
- Use descriptive function names with action + data + ToLS/FromLS pattern
- Provide EventTarget for storage change notifications
- Return empty string as fallback for getters
- Group related storage operations (clear all related items)
- Use consistent key naming (snake_case for storage keys)

## OAuth and URL Generation

### OAuth URL Construction
```typescript
// ✅ Good - OAuth URL generation pattern
export const getGoogleAuthUrl = () => {
  const url = 'https://accounts.google.com/o/oauth2/v2/auth'

  const query = {
    client_id: envConfig.googleClientId,
    redirect_uri: envConfig.googleRedirectUri,
    response_type: 'code',
    scope: ['https://www.googleapis.com/auth/userinfo.profile', 'https://www.googleapis.com/auth/userinfo.email'].join(' '),
    prompt: 'consent',
    access_type: 'offline'
  }

  const queryString = new URLSearchParams(query).toString()
  return `${url}?${queryString}`
}
```

**OAuth Conventions:**
- Use configuration constants for client credentials
- Build query parameters as objects before stringifying
- Use `URLSearchParams` for safe query string construction
- Include necessary OAuth scopes and parameters
- Join array-based parameters with appropriate separators

## Data Generation and Manipulation

### Color Generation Algorithm
```typescript
// ✅ Good - Deterministic color generation
export const generateColorFromString = (string: string) => {
  let hash = 0

  for (let i = 0; i < string.length; i++) {
    hash = string.charCodeAt(i) + ((hash << 5) - hash)
  }

  const color = `#${((hash >> 16) & 0xff).toString(16).padStart(2, '0')}${((hash >> 8) & 0xff)
    .toString(16)
    .padStart(2, '0')}${(hash & 0xff).toString(16).padStart(2, '0')}`

  return color
}
```

### Mock Data Generation
```typescript
// ✅ Good - Placeholder data generation
export const generatePlaceholderCard = (column: ColumnType) => {
  return {
    _id: `${column._id}-placeholder-card`,
    board_id: column.board_id,
    column_id: column._id,
    title: 'Placeholder Card Title',
    description: 'Placeholder Card Description',
    cover_photo: '',
    members: [],
    comments: [],
    attachments: [],
    _destroy: false,
    created_at: new Date(),
    updated_at: new Date(),
    FE_PlaceholderCard: true  // Frontend-only flag
  }
}
```

**Data Generation Conventions:**
- Use deterministic algorithms for consistent results
- Include all required fields for type compatibility
- Use descriptive identifiers for generated data
- Add frontend-specific flags when needed (FE_ prefix)
- Generate IDs using predictable patterns

## Array Sorting and Ordering

### Generic Sorting Utilities
```typescript
// ✅ Good - Generic array ordering with comprehensive typing
/**
 * Maps an array of objects to a new array sorted based on a specified order.
 */
export const mapOrder = <T extends Record<K, V>, K extends string | number | symbol, V>(
  originalArray: T[] | undefined | null,
  orderArray: V[] | undefined | null,
  key: K
): T[] => {
  if (!originalArray || !orderArray || !key) return []

  return [...originalArray].sort((a, b) => orderArray.indexOf(a[key]) - orderArray.indexOf(b[key]))
}
```

**Sorting Conventions:**
- Provide comprehensive JSDoc documentation
- Use generic types for maximum reusability
- Handle null/undefined input gracefully
- Return new arrays (immutable operations)
- Use descriptive type constraints (`T extends Record<K, V>`)

## String Formatting Utilities

### Text Formatting Patterns
```typescript
// ✅ Good - Safe string formatting
export const capitalizeFirstLetter = (value: string) => {
  return value ? `${value.charAt(0).toUpperCase()}${value.slice(1)}` : ''
}
```

**Formatting Conventions:**
- Handle empty/null strings gracefully
- Return empty string as fallback
- Use template literals for string construction
- Keep formatting functions pure (no side effects)

## DOM Manipulation Utilities

### Loading State Management
```typescript
// ✅ Good - DOM utility with proper typing
interface InterceptorLoadingElement extends HTMLElement {
  style: CSSStyleDeclaration
}

export const interceptorLoadingElements = (calling: boolean) => {
  const elements: NodeListOf<InterceptorLoadingElement> = document.querySelectorAll('.interceptor-loading')

  for (let i = 0; i < elements.length; i++) {
    if (calling) {
      elements[i].style.opacity = '0.5'
      elements[i].style.pointerEvents = 'none'
    } else {
      elements[i].style.opacity = 'initial'
      elements[i].style.pointerEvents = 'initial'
    }
  }
}
```

**DOM Manipulation Conventions:**
- Define interfaces for extended HTML elements
- Use descriptive CSS class selectors
- Handle multiple elements with loops
- Use semantic boolean parameters
- Reset styles to 'initial' rather than specific values

## Import and Dependency Patterns

### Import Organization
```typescript
// ✅ Good - Import pattern for utilities
import axios, { AxiosError } from 'axios'
import { FetchBaseQueryError } from '@reduxjs/toolkit/query'
import { config, envConfig } from '~/constants/config'
import HttpStatusCode from '~/constants/http-status-code'
import { ColumnType } from '~/schemas/column.schema'
import { EntityError } from '~/types/utils.type'

// Group imports by:
// 1. External libraries
// 2. Internal constants and configuration
// 3. Internal types and schemas
```

## Function Documentation

### JSDoc Standards
```typescript
// ✅ Good - Comprehensive JSDoc documentation
/**
 * Maps an array of objects to a new array sorted based on a specified order.
 *
 * @template T - Type of the objects in the array extending Record<K, V>
 * @template K - Type of the key in the object (string | number | symbol)
 * @template V - Type of the value associated with the key
 *
 * @param {T[] | undefined | null} originalArray - The original array to be sorted
 * @param {V[] | undefined | null} orderArray - Array that defines the desired order of elements
 * @param {K} key - The key in the objects whose value is used for ordering
 *
 * @returns {T[]} A new sorted array based on the order specified in orderArray
 *
 * @example
 * // Sorting cards based on columnId order
 * const sortedCards = mapOrder(cards, columnOrder, 'columnId');
 */
```

**Documentation Conventions:**
- Include template parameter descriptions
- Document all parameters with types and descriptions
- Provide clear return type documentation
- Include practical usage examples
- Explain the purpose and behavior clearly

## File Organization Guidelines

### When to Create New Utility Files

1. **Domain separation**: Create separate files for distinct functional domains
2. **Size consideration**: If a utility file exceeds 100-150 lines, consider splitting
3. **Dependency patterns**: Group utilities that share similar dependencies
4. **Usage patterns**: Group functions that are commonly used together

### Utility File Patterns

```typescript
// ✅ Good - File exports pattern
// Multiple related functions
export const functionOne = () => { ... }
export const functionTwo = () => { ... }
export const functionThree = () => { ... }

// Single primary utility (if applicable)
const mainUtility = () => { ... }
export default mainUtility
export { helperFunction }
```

## Testing Considerations

### Utility Function Testing
- Pure functions should be easily unit testable
- Include edge case testing for validation functions
- Test error handling in type guard functions
- Verify DOM manipulation utilities in integration tests
- Mock localStorage for storage utility tests

## Performance Guidelines

### Optimization Patterns
- Use memoization for expensive computations
- Prefer immutable operations (return new arrays/objects)
- Handle large datasets efficiently in sorting utilities
- Minimize DOM queries in manipulation utilities
- Cache results when appropriate

## Common Anti-Patterns to Avoid

### Poor Error Handling
```typescript
// ❌ Bad - No error handling or edge cases
export const validateFile = (file: File) => {
  return file.size > 1000000 ? 'Too large' : null
}

// ✅ Good - Comprehensive validation
export const validateFile = (file: File) => {
  if (!file) return 'No file provided'
  if (file.size > config.maxSize) return `File too large (max: ${config.maxSize}MB)`
  if (!allowedTypes.includes(file.type)) return 'Invalid file type'
  return null
}
```

### Missing Type Safety
```typescript
// ❌ Bad - No type safety
export const sortArray = (arr: any[], order: any[], key: string) => {
  return arr.sort((a, b) => order.indexOf(a[key]) - order.indexOf(b[key]))
}

// ✅ Good - Type-safe implementation
export const mapOrder = <T extends Record<K, V>, K extends string | number | symbol, V>(
  originalArray: T[] | undefined | null,
  orderArray: V[] | undefined | null,
  key: K
): T[] => {
  if (!originalArray || !orderArray || !key) return []
  return [...originalArray].sort((a, b) => orderArray.indexOf(a[key]) - orderArray.indexOf(b[key]))
}
```

### Side Effects in Pure Functions
```typescript
// ❌ Bad - Side effects in utility
export const processData = (data: any[]) => {
  data.push({ id: 'new' })  // Mutating input
  console.log('Processing...')  // Logging side effect
  return data
}

// ✅ Good - Pure function
export const processData = (data: any[]) => {
  return [...data, { id: 'new' }]  // Return new array
}
```

This structure ensures consistent, type-safe, and maintainable utility functions across the entire Trellone application.
