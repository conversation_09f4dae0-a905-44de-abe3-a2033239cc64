import { useColorScheme } from '@mui/material'
import Box from '@mui/material/Box'
import MDEditor from '@uiw/react-md-editor'
import { useEffect, useRef, useState } from 'react'
import LinkPreviewPopup, { LinkPreviewData } from '~/components/Modal/ActiveCard/CardActivitySection/LinkPreviewPopup'
import { isValidUrl } from '~/utils/url'

interface MarkdownWithLinkPreviewProps {
  source: string
  style?: React.CSSProperties
}

export default function MarkdownWithLinkPreview({ source, style }: MarkdownWithLinkPreviewProps) {
  const { mode } = useColorScheme()
  const containerRef = useRef<HTMLDivElement>(null)
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [linkPreviewAnchor, setLinkPreviewAnchor] = useState<HTMLElement | null>(null)
  const [linkPreviewData, setLinkPreviewData] = useState<LinkPreviewData | null>(null)

  const handleLinkMouseEnter = (event: MouseEvent, url: string) => {
    // Clear any existing timeout
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
    }

    // Set a delay before showing the preview
    hoverTimeoutRef.current = setTimeout(() => {
      const target = event.target as HTMLElement
      setLinkPreviewAnchor(target)
      setLinkPreviewData({
        url,
        title: '',
        description: '',
        domain: ''
      })
    }, 500) // 500ms delay as specified in requirements
  }

  const handleLinkMouseLeave = () => {
    // Clear the timeout if mouse leaves before delay completes
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
      hoverTimeoutRef.current = null
    }

    // Hide the preview
    setLinkPreviewAnchor(null)
    setLinkPreviewData(null)
  }

  const handlePreviewMouseEnter = () => {
    // Keep the preview open when hovering over it
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
      hoverTimeoutRef.current = null
    }
  }

  const handlePreviewMouseLeave = () => {
    // Hide the preview when leaving the preview popup
    setLinkPreviewAnchor(null)
    setLinkPreviewData(null)
  }

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    // Function to add event listeners to all links
    const addLinkEventListeners = () => {
      const links = container.querySelectorAll('a[href]')
      
      links.forEach((link) => {
        const href = link.getAttribute('href')
        if (!href || !isValidUrl(href)) return

        const handleMouseEnter = (event: Event) => {
          handleLinkMouseEnter(event as MouseEvent, href)
        }

        const handleMouseLeave = () => {
          handleLinkMouseLeave()
        }

        // Remove existing listeners to avoid duplicates
        link.removeEventListener('mouseenter', handleMouseEnter)
        link.removeEventListener('mouseleave', handleMouseLeave)

        // Add new listeners
        link.addEventListener('mouseenter', handleMouseEnter)
        link.addEventListener('mouseleave', handleMouseLeave)
      })
    }

    // Add listeners initially
    addLinkEventListeners()

    // Use MutationObserver to detect when content changes and re-add listeners
    const observer = new MutationObserver(() => {
      addLinkEventListeners()
    })

    observer.observe(container, {
      childList: true,
      subtree: true
    })

    // Cleanup function
    return () => {
      observer.disconnect()
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current)
      }

      // Remove all event listeners
      const links = container.querySelectorAll('a[href]')
      links.forEach((link) => {
        const href = link.getAttribute('href')
        if (!href || !isValidUrl(href)) return

        const handleMouseEnter = (event: Event) => {
          handleLinkMouseEnter(event as MouseEvent, href)
        }

        const handleMouseLeave = () => {
          handleLinkMouseLeave()
        }

        link.removeEventListener('mouseenter', handleMouseEnter)
        link.removeEventListener('mouseleave', handleMouseLeave)
      })
    }
  }, [source]) // Re-run when source changes

  return (
    <>
      <Box ref={containerRef} data-color-mode={mode}>
        <MDEditor.Markdown
          source={source}
          style={{
            whiteSpace: 'pre-wrap',
            padding: '0px',
            border: 'none',
            borderRadius: '0px',
            backgroundColor: 'transparent',
            ...style
          }}
        />
      </Box>

      <LinkPreviewPopup
        open={Boolean(linkPreviewAnchor && linkPreviewData)}
        anchorEl={linkPreviewAnchor}
        linkData={linkPreviewData}
        onMouseEnter={handlePreviewMouseEnter}
        onMouseLeave={handlePreviewMouseLeave}
      />
    </>
  )
}
