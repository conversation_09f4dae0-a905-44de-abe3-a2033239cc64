---
description: TypeScript types and interfaces best practices
globs:
alwaysApply: true
---
# TypeScript Types & Interfaces Best Practices

This rule defines the comprehensive patterns and conventions for TypeScript type definitions in the Trellone React application, based on the established patterns in [src/types](mdc:src/types).

## File Organization and Naming

### Type File Structure
All type definitions are organized in [src/types/](mdc:src/types) with specific files for different domains:

- [jwt.type.ts](mdc:src/types/jwt.type.ts) - JWT and authentication-related types
- [query-params.type.ts](mdc:src/types/query-params.type.ts) - URL query parameter interfaces
- [utils.type.ts](mdc:src/types/utils.type.ts) - General utility types and error handling

### Naming Conventions

**File Naming:**
```typescript
// ✅ Good - Domain-specific with .type.ts suffix
jwt.type.ts
query-params.type.ts
utils.type.ts

// ❌ Bad - Generic or unclear naming
types.ts
interfaces.ts
common.ts
```

**Type and Interface Naming:**
```typescript
// ✅ Good - Descriptive names with Type/Payload suffixes
export type TokenTypeValue = (typeof TokenType)[keyof typeof TokenType]
export interface TokenPayload { ... }
export interface InviteTokenPayload extends TokenPayload { ... }

// ❌ Bad - Vague or inconsistent naming
export type Token = ...
export interface Data { ... }
```

## Type Definition Patterns

### Constant-Based Type Generation

**Extracting Types from Constants:**
```typescript
// ✅ Good - Generate types from const assertions
import { TokenType, UserVerifyStatus } from '~/constants/type'

export type TokenTypeValue = (typeof TokenType)[keyof typeof TokenType]
export type UserVerifyStatusType = (typeof UserVerifyStatus)[keyof typeof UserVerifyStatus]

// Usage in interfaces
export interface TokenPayload {
  user_id: string
  token_type: TokenTypeValue  // Ensures type safety with constants
  verify: UserVerifyStatusType
  exp: number
  iat: number
}
```

**Type Generation Best Practices:**
- Always use `(typeof ConstantObject)[keyof typeof ConstantObject]` pattern
- Import constants from [constants/type.ts](mdc:src/constants/type.ts)
- Use descriptive type names that indicate their source constant
- Prefer type aliases over enums for constant-based types

### Interface Inheritance Patterns

**Base Interface Extension:**
```typescript
// ✅ Good - Clear inheritance hierarchy
export interface CommonQueryParams {
  page?: number | string
  limit?: number | string
}

export interface BoardQueryParams extends CommonQueryParams {
  keyword?: string
}

export interface AuthQueryParams {
  registered_email?: string
  verified_email?: string
  forgot_password_token?: string
  token?: string
  email?: string
}
```

**Extension Conventions:**
- Create base interfaces for common properties (`CommonQueryParams`)
- Use `extends` for logical inheritance relationships
- Keep domain-specific interfaces separate when they don't share properties
- Use optional properties (`?`) for query parameters and optional fields

### Schema-Type Integration

**Zod Schema Type Extraction:**
```typescript
// ✅ Good - Import schema types from schemas
import { UserType } from '~/schemas/user.schema'

export interface OAuthQueryParams {
  access_token?: string
  refresh_token?: string
  new_user?: number
  verify?: UserType['verify']  // Extract specific property type
}
```

**Schema Integration Best Practices:**
- Import types from schema files using `z.TypeOf<typeof Schema>`
- Use property extraction syntax for specific fields: `UserType['verify']`
- Maintain consistency between schema definitions and type usage
- Prefer schema-generated types over manual type definitions

## Query Parameter Type Patterns

### URL Parameter Interfaces

**Standard Query Parameter Structure:**
```typescript
// ✅ Good - Consistent query parameter patterns
export interface CommonQueryParams {
  page?: number | string      // Pagination - allow both types
  limit?: number | string     // Pagination - allow both types
}

export interface AuthQueryParams {
  registered_email?: string   // Optional auth parameters
  verified_email?: string
  forgot_password_token?: string
  token?: string
  email?: string
}

export interface BoardInvitationQueryParams {
  token?: string             // Invitation-specific parameters
  board_id?: string
}
```

**Query Parameter Conventions:**
- Use optional properties (`?`) for all query parameters
- Allow both `number | string` for numeric parameters (URL parsing flexibility)
- Group related parameters in domain-specific interfaces
- Use snake_case for parameter names to match API conventions
- Extend `CommonQueryParams` for interfaces that need pagination

### Parameter Type Flexibility

**Handle URL Parsing Ambiguity:**
```typescript
// ✅ Good - Account for URL string parsing
export interface CommonQueryParams {
  page?: number | string    // URLs parse to strings, but app logic uses numbers
  limit?: number | string   // Allow both for maximum flexibility
}

// ❌ Bad - Too restrictive for URL parameters
export interface CommonQueryParams {
  page?: number            // Will cause type errors with URL parsing
  limit?: number
}
```

## Error Handling Type Patterns

### Generic Error Types

**Validation Error Structure:**
```typescript
// ✅ Good - Generic error handling with proper typing
type EntityValidationErrors<T = Record<string, any>> = {
  message: string
  errors?: {
    [K in keyof T]?: {
      type: string
      value: string
      msg: string
      path: string
      location: string
    }
  }
}

export interface EntityError<T = Record<string, any>> {
  status: number
  data: EntityValidationErrors<T>
}
```

**Error Type Conventions:**
- Use generic types (`<T>`) for reusable error structures
- Provide sensible defaults: `T = Record<string, any>`
- Use mapped types for field-specific errors: `[K in keyof T]?`
- Include all relevant error metadata (type, value, msg, path, location)
- Make error properties optional where appropriate

### Union Type Patterns

**Simple Union Types:**
```typescript
// ✅ Good - Clear union types for simple cases
export type Mode = 'light' | 'dark' | 'system'

// Usage in components
interface ThemeProps {
  mode: Mode  // Restricts to specific values
}
```

**Union Type Best Practices:**
- Use string literals for simple enumerations
- Prefer union types over enums for simple value sets
- Use descriptive type names that indicate the domain
- Keep union types in appropriate domain files

## JWT and Authentication Types

### Token Payload Structure

**JWT Token Interfaces:**
```typescript
// ✅ Good - Complete token payload definition
export interface TokenPayload {
  user_id: string                    // Required user identifier
  token_type: TokenTypeValue         // Type from constants
  verify: UserVerifyStatusType       // Verification status
  exp: number                        // Expiration timestamp
  iat: number                        // Issued at timestamp
}

export interface InviteTokenPayload extends TokenPayload {
  inviter_id: string                 // Additional fields for specific token types
  invitation_id: string
}
```

**JWT Type Conventions:**
- Include all standard JWT claims (exp, iat)
- Use meaningful property names (user_id, not sub)
- Extend base interfaces for specialized token types
- Use imported constant types for enum-like values
- Include domain-specific claims in extended interfaces

## Import and Dependency Patterns

### Import Organization

**Type File Import Structure:**
```typescript
// ✅ Good - Clear import organization
import { TokenType, UserVerifyStatus } from '~/constants/type'  // Constants first
import { UserType } from '~/schemas/user.schema'                // Schema types second

// Type definitions follow
export type TokenTypeValue = (typeof TokenType)[keyof typeof TokenType]
```

**Import Conventions:**
- Import constants from [constants/type.ts](mdc:src/constants/type.ts) first
- Import schema-generated types from schema files second
- Use path aliases (`~/`) for all internal imports
- Group imports by source type (constants, schemas, external libraries)

### Cross-File Type Dependencies

**Type Composition Patterns:**
```typescript
// ✅ Good - Compose types from multiple sources
import { UserType } from '~/schemas/user.schema'
import { TokenType } from '~/constants/type'

export interface ComplexType {
  user: UserType                     // From schema
  token_type: TokenTypeValue         // From constants
  custom_field: string               // Local definition
}
```

## Type Safety and Validation

### Strict Type Checking

**Ensure Type Safety:**
```typescript
// ✅ Good - Strict typing with proper constraints
export interface EntityError<T = Record<string, any>> {
  status: number
  data: EntityValidationErrors<T>
}

// ✅ Good - Use keyof for mapped types
type EntityValidationErrors<T = Record<string, any>> = {
  errors?: {
    [K in keyof T]?: ErrorDetail
  }
}
```

**Type Safety Conventions:**
- Use generic constraints where appropriate
- Leverage TypeScript's mapped types for dynamic interfaces
- Provide reasonable default generic parameters
- Use `keyof` operator for type-safe property access
- Prefer interfaces over types for object shapes

### Optional vs Required Properties

**Property Optionality Patterns:**
```typescript
// ✅ Good - Clear optionality based on usage
export interface TokenPayload {
  user_id: string                    // Required - always present
  token_type: TokenTypeValue         // Required - always present
  verify: UserVerifyStatusType       // Required - always present
  exp: number                        // Required - JWT standard
  iat: number                        // Required - JWT standard
}

export interface QueryParams {
  page?: number | string             // Optional - URL parameters
  limit?: number | string            // Optional - URL parameters
  keyword?: string                   // Optional - search parameters
}
```

**Optionality Guidelines:**
- Make properties optional only when they're truly optional in usage
- Required for core business logic fields
- Optional for URL parameters, search filters, and user inputs
- Optional for fields that have sensible defaults
- Required for JWT standard claims and essential identifiers

## Documentation and Comments

### Type Documentation

**JSDoc for Complex Types:**
```typescript
/**
 * Represents a JWT token payload with user authentication information
 * @interface TokenPayload
 */
export interface TokenPayload {
  /** Unique identifier for the authenticated user */
  user_id: string
  /** Type of token from TokenType constants */
  token_type: TokenTypeValue
  /** User verification status */
  verify: UserVerifyStatusType
  /** Token expiration timestamp (Unix timestamp) */
  exp: number
  /** Token issued at timestamp (Unix timestamp) */
  iat: number
}
```

**Documentation Conventions:**
- Use JSDoc comments for complex or non-obvious types
- Document the purpose and usage of generic parameters
- Explain the relationship between types and their source constants
- Include examples for complex type usage
- Document any constraints or validation rules

## File Organization Guidelines

### When to Create New Type Files

1. **Domain separation**: Create separate files for distinct business domains
2. **Size consideration**: Split files when they exceed 100-150 lines
3. **Dependency patterns**: Group types that share similar import dependencies
4. **Usage patterns**: Group types that are commonly used together

### Type Export Patterns

```typescript
// ✅ Good - Named exports for all types
export type TokenTypeValue = (typeof TokenType)[keyof typeof TokenType]
export type UserVerifyStatusType = (typeof UserVerifyStatus)[keyof typeof UserVerifyStatus]
export interface TokenPayload { ... }
export interface InviteTokenPayload extends TokenPayload { ... }

// ❌ Bad - Default exports for type files
export default interface TokenPayload { ... }
```

## Common Anti-Patterns to Avoid

### Poor Type Safety
```typescript
// ❌ Bad - Using any type
export interface TokenPayload {
  user_id: string
  token_type: any  // Loses type safety
  verify: any      // No validation
}

// ✅ Good - Proper type constraints
export interface TokenPayload {
  user_id: string
  token_type: TokenTypeValue      // Type-safe constant
  verify: UserVerifyStatusType    // Validated enum type
}
```

### Inconsistent Naming
```typescript
// ❌ Bad - Inconsistent naming patterns
export type tokenType = ...      // camelCase
export interface Token_Payload { ... }  // snake_case
export type TokenData = ...      // Inconsistent suffix

// ✅ Good - Consistent naming
export type TokenTypeValue = ...
export interface TokenPayload { ... }
export interface InviteTokenPayload extends TokenPayload { ... }
```

### Missing Relationships
```typescript
// ❌ Bad - Duplicate type definitions
export interface AuthParams {
  page?: number
  limit?: number
  token?: string
}

export interface BoardParams {
  page?: number      // Duplicated
  limit?: number     // Duplicated
  keyword?: string
}

// ✅ Good - Proper inheritance
export interface CommonQueryParams {
  page?: number | string
  limit?: number | string
}

export interface AuthQueryParams {
  token?: string
  email?: string
}

export interface BoardQueryParams extends CommonQueryParams {
  keyword?: string
}
```

### Improper Generic Usage
```typescript
// ❌ Bad - Overly complex or unnecessary generics
export interface SimpleData<T, U, V> {
  id: string
  name: string
}

// ✅ Good - Generics only when needed
export interface EntityError<T = Record<string, any>> {
  status: number
  data: EntityValidationErrors<T>
}
```

This structure ensures type-safe, maintainable, and consistent TypeScript type definitions across the entire React application, following the established patterns in the Trellone project.
