# Link Preview Popup Feature

## Overview

This feature adds link preview popups to the CardActivitySection component. When users hover over URLs or links within comments, a preview popup appears showing website information including favicon, title, description, and thumbnail.

## Implementation Details

### Components Created

1. **LinkPreviewPopup** (`src/components/Modal/ActiveCard/CardActivitySection/LinkPreviewPopup/`)
   - Main popup component that displays link preview information
   - Includes favicon, title, description, thumbnail, and action buttons
   - Styled to match the existing Trello-like UI design system
   - Uses mock data for realistic previews

2. **MarkdownWithLinkPreview** (`src/components/Modal/ActiveCard/CardActivitySection/MarkdownWithLinkPreview/`)
   - Wrapper component for MDEditor.Markdown that adds link preview functionality
   - Uses MutationObserver to detect dynamically added links
   - Handles hover events with proper timing and positioning

### Utilities Enhanced

3. **URL Detection Utilities** (`src/utils/url.ts`)
   - `detectLinksInContent()` - Detects both plain URLs and markdown links
   - `isValidUrl()` - Validates URL format
   - `extractFirstUrl()` - Extracts the first URL from content

### Features

- **Hover Trigger**: 500ms delay before showing preview (as specified)
- **Smart Positioning**: Uses Popper.js for intelligent positioning
- **Responsive Design**: Adapts to different screen sizes
- **Mock Data**: Realistic preview data for popular websites
- **Accessibility**: Proper ARIA attributes and keyboard navigation
- **Performance**: Efficient event handling and cleanup

### Supported Link Types

- Plain URLs: `https://github.com/microsoft/vscode`
- Markdown links: `[GitHub](https://github.com)`
- Mixed content with multiple links

### Mock Data Domains

The feature includes realistic mock data for:
- GitHub
- Stack Overflow
- YouTube
- Medium
- Figma
- Canva
- Notion
- Trello
- Slack
- Discord
- LinkedIn
- Twitter/X
- Google services (Docs, Drive)

## Usage

The feature is automatically enabled in the CardActivitySection component. Users can:

1. **Hover over any link** in a comment to see the preview
2. **Click "Open preview"** to open the link in a new tab
3. **Click "Copy link"** to copy the URL to clipboard
4. **Move mouse away** to hide the preview

## Testing

To test the feature:

1. Open a card with comments containing links
2. Hover over any URL or markdown link
3. Verify the preview appears after 500ms
4. Test different link types and domains
5. Verify proper positioning and styling

### Sample Test Comments

The mock data includes test comments with various link formats:

```markdown
Check out this GitHub repository: https://github.com/microsoft/vscode - it has great features!

Here are some useful resources:
- [Figma Design](https://figma.com) for UI mockups
- [Stack Overflow](https://stackoverflow.com) for coding help
- https://youtube.com for tutorials

Also check out https://trello.com for project management!
```

## Technical Notes

- Uses React hooks for state management
- Implements proper cleanup to prevent memory leaks
- Follows existing code patterns and styling conventions
- Compatible with dark/light theme modes
- Uses existing Material-UI components for consistency

## Future Enhancements

- Real URL metadata fetching (replace mock data)
- Caching mechanism for fetched previews
- Custom preview templates for different content types
- Preview for image URLs
- Video preview support
