---
description: Redux Toolkit & RTK Query best practices
globs:
alwaysApply: true
---
# Redux Toolkit & RTK Query Best Practices

This rule defines the comprehensive patterns for state management using Redux Toolkit and API handling with RTK Query in the Trellone React application.

## Store Configuration Patterns

### Store Setup ([store.ts](mdc:src/lib/redux/store.ts))

**Complete Store Configuration:**
```typescript
// ✅ Good - Comprehensive store setup
import { configureStore } from '@reduxjs/toolkit'
import { persistReducer, persistStore } from 'redux-persist'
import storage from 'redux-persist/lib/storage'

const rootPersistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth'] // Only persist auth state
}

const persistedReducers = persistReducer(rootPersistConfig, rootReducer)

const apiMiddlewares = [
  boardApi.middleware,
  columnApi.middleware,
  cardApi.middleware,
  authApi.middleware,
  userApi.middleware,
  mediaApi.middleware,
  invitationApi.middleware
]

export const store = configureStore({
  reducer: persistedReducers,
  middleware: (getDefaultMiddleware) => 
    getDefaultMiddleware({ serializableCheck: false }).concat(apiMiddlewares),
  devTools: process.env.NODE_ENV !== 'production'
})

export type RootState = ReturnType<AppStore['getState']>
export type AppDispatch = AppStore['dispatch']
```

**Store Configuration Best Practices:**
- Use `persistReducer` for selective state persistence
- Disable `serializableCheck` when using redux-persist
- Include all RTK Query middleware in the store
- Enable DevTools only in development
- Export proper TypeScript types for state and dispatch

### Typed Hooks ([hooks.ts](mdc:src/lib/redux/hooks.ts))

```typescript
// ✅ Good - Typed Redux hooks
import { useDispatch, useSelector } from 'react-redux'
import type { AppDispatch, RootState } from './store'

export const useAppDispatch = useDispatch.withTypes<AppDispatch>()
export const useAppSelector = useSelector.withTypes<RootState>()
```

## RTK Query API Setup

### Base Query Configuration ([helpers.ts](mdc:src/lib/redux/helpers.ts))

```typescript
// ✅ Good - Custom axios base query
import type { BaseQueryFn } from '@reduxjs/toolkit/query'
import type { AxiosRequestConfig, AxiosError } from 'axios'
import http from '~/lib/http'

const axiosBaseQuery =
  (): BaseQueryFn<
    {
      url: AxiosRequestConfig['url']
      method?: AxiosRequestConfig['method']
      data?: AxiosRequestConfig['data']
      params?: AxiosRequestConfig['params']
      headers?: AxiosRequestConfig['headers']
    },
    unknown,
    unknown
  > =>
  async ({ url, method, data, params, headers }) => {
    try {
      const result = await http.request({
        url,
        method,
        data,
        params,
        headers
      })
      return { data: result.data }
    } catch (axiosError) {
      const error = axiosError as AxiosError
      return {
        error: {
          status: error.response?.status,
          data: error.response?.data || error.message
        }
      }
    }
  }
```

**Base Query Conventions:**
- Use custom axios base query for consistent HTTP handling
- Properly type the base query function
- Handle both success and error cases
- Extract relevant error information from axios responses

### API Slice Structure

**Standard API Slice Pattern:**
```typescript
// ✅ Good - Complete API slice structure
import { createApi } from '@reduxjs/toolkit/query/react'
import { toast } from 'react-toastify'
import axiosBaseQuery from '~/lib/redux/helpers'

const ENTITY_API_URL = '/entity' as const
const reducerPath = 'entity/api' as const
const tagTypes = ['Entity'] as const

export const entityApi = createApi({
  reducerPath,
  tagTypes,
  baseQuery: axiosBaseQuery(),
  endpoints: (build) => ({
    // Endpoints definition
  })
})

export const { useEntityQuery, useEntityMutation } = entityApi
export default entityApi.reducer
```

**API Slice Conventions:**
- Use const assertions for URL, reducer path, and tag types
- Follow consistent naming pattern: `entityApi`
- Export generated hooks and reducer separately
- Use descriptive reducer paths: `'entity/api'`

## Query Endpoints

### Query Patterns ([boards.ts](mdc:src/queries/boards.ts))

```typescript
// ✅ Good - Query with proper caching
getBoards: build.query<BoardListResType, BoardQueryParams>({
  query: (params) => ({ url: BOARD_API_URL, method: 'GET', params }),
  providesTags: (result) =>
    result
      ? [
          ...result.result.boards.map(({ _id }) => ({ type: 'Board' as const, id: _id })),
          { type: 'Board' as const, id: 'LIST' }
        ]
      : [{ type: 'Board' as const, id: 'LIST' }]
})
```

**Query Best Practices:**
- Use proper TypeScript generics for response and request types
- Implement `providesTags` for automatic cache invalidation
- Use both specific item tags and LIST tags
- Handle cases where result might be undefined

## Mutation Endpoints

### Mutation with onQueryStarted ([auth.ts](mdc:src/queries/auth.ts))

```typescript
// ✅ Good - Mutation with side effects
login: build.mutation<AuthResType, LoginBodyType>({
  query: (body) => ({ url: `${AUTH_API_URL}/login`, method: 'POST', data: body }),
  async onQueryStarted(_args, { dispatch, queryFulfilled }) {
    try {
      await queryFulfilled

      dispatch(userApi.endpoints.getMe.initiate(undefined)).then((res) => {
        if (!res.error) {
          const profile = res.data?.result
          dispatch(setAuthenticated(true))
          dispatch(setProfile(profile))
        }
      })
    } catch (error: any) {
      console.error(error)
    }
  }
}),

// ✅ Good - Mutation with cache invalidation
updateBoard: build.mutation<BoardResType, { id: string; body: UpdateBoardBodyType }>({
  query: ({ id, body }) => ({ url: `${BOARD_API_URL}/${id}`, method: 'PUT', data: body }),
  async onQueryStarted(_args, { queryFulfilled }) {
    try {
      await queryFulfilled
    } catch (error) {
      toast.error('There was an error updating the board')
      console.error(error)
    }
  },
  invalidatesTags: (_result, _error, { id }) => [
    { type: 'Board', id },
    { type: 'Board', id: 'LIST' }
  ]
})
```

**Mutation Best Practices:**
- Use `onQueryStarted` for side effects and state updates
- Implement proper error handling with user feedback
- Use `invalidatesTags` for automatic cache updates
- Handle both successful and error cases
- Update related slices when necessary

### Toast Integration Pattern

```typescript
// ✅ Good - Toast integration in mutations
register: build.mutation<RegisterResType, RegisterBodyType>({
  query: (body) => ({ url: `${AUTH_API_URL}/register`, method: 'POST', data: body }),
  async onQueryStarted(_args, { queryFulfilled }) {
    try {
      const { data } = await queryFulfilled
      toast.success(data.message)
    } catch (error: any) {
      console.error(error)
    }
  }
})
```

## Redux Slice Patterns

### Standard Slice Structure ([auth.slice.ts](mdc:src/store/slices/auth.slice.ts))

```typescript
// ✅ Good - Simple slice structure
import { createSlice } from '@reduxjs/toolkit'

interface AuthSliceState {
  isAuthenticated: boolean
  profile: UserType | null
}

const initialState: AuthSliceState = {
  profile: null,
  isAuthenticated: false
}

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setAuthenticated: (state, action) => {
      state.isAuthenticated = action.payload
    },
    setProfile: (state, action) => {
      state.profile = action.payload
    },
    reset: (state) => {
      state.isAuthenticated = false
      state.profile = null
    }
  }
})

export const { setAuthenticated, setProfile, reset } = authSlice.actions
export default authSlice.reducer
```

### AsyncThunk Slice Pattern ([board.slice.ts](mdc:src/store/slices/board.slice.ts))

```typescript
// ✅ Good - Slice with async thunk
export const getBoardDetails = createAsyncThunk(
  'board/getBoardDetails', 
  async (boardId: string, thunkAPI) => {
    // Clear state when starting new request
    thunkAPI.dispatch(boardSlice.actions.clearActiveBoard())
    
    const response = await http.get<BoardResType>(
      `${envConfig.baseUrl}/boards/${boardId}`, 
      { signal: thunkAPI.signal }
    )
    return response.data
  }
)

export const boardSlice = createSlice({
  name: 'board',
  initialState,
  reducers: {
    updateActiveBoard: (state, action: PayloadAction<BoardResType['result'] | null>) => {
      state.activeBoard = action.payload
    },
    clearActiveBoard: (state) => {
      state.activeBoard = null
      state.error = null
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getBoardDetails.pending, (state, action) => {
        if (state.loading === 'idle') {
          state.loading = 'pending'
          state.currentRequestId = action.meta.requestId
          state.error = null
        }
      })
      .addCase(getBoardDetails.fulfilled, (state, action) => {
        const { requestId } = action.meta
        if (state.loading === 'pending' && state.currentRequestId === requestId) {
          state.loading = 'idle'
          state.activeBoard = processedData
          state.currentRequestId = undefined
          state.error = null
        }
      })
      .addCase(getBoardDetails.rejected, (state, action) => {
        const { requestId } = action.meta
        if (state.loading === 'pending' && state.currentRequestId === requestId) {
          state.loading = 'idle'
          state.activeBoard = null
          state.currentRequestId = undefined
          state.error = action.error.message || 'Failed to load'
        }
      })
  }
})
```

**Slice Best Practices:**
- Define proper TypeScript interfaces for state
- Use `PayloadAction` for action typing
- Implement loading states with request ID tracking
- Clear related state when starting new requests
- Process data in fulfilled cases
- Handle cancellation and errors properly

## Component Integration Patterns

### Hook Usage in Components

```typescript
// ✅ Good - Component integration pattern
export default function Component() {
  const dispatch = useAppDispatch()
  const { activeBoard, loading, error } = useAppSelector((state) => state.board)
  const { profile } = useAppSelector((state) => state.auth)
  const { socket } = useAppSelector((state) => state.app)

  // RTK Query hooks
  const [updateBoardMutation] = useUpdateBoardMutation()
  const [moveCardMutation] = useMoveCardToDifferentColumnMutation()

  // Generic update handler
  const handleUpdateActiveCard = async (body: UpdateCardBodyType) => {
    const updateCardRes = await updateCardMutation({ 
      id: activeCard?._id as string, 
      body 
    }).unwrap()

    const updatedCard = updateCardRes.result

    // Update local state
    dispatch(updateActiveCard(updatedCard))
    
    // Update global state
    dispatch(updateCardInBoard(updatedCard))

    // Emit socket event for real-time updates
    socket?.emit('CLIENT_USER_UPDATED_CARD', updatedCard)

    return updatedCard
  }

  // Loading and error handling
  if (loading === 'pending') {
    return <PageLoadingSpinner caption="Loading..." />
  }

  if (error || !activeBoard) {
    return <ErrorComponent />
  }

  return <MainContent />
}
```

**Component Integration Best Practices:**
- Use typed hooks (`useAppDispatch`, `useAppSelector`)
- Destructure state clearly and descriptively
- Group mutation hooks together
- Create reusable update handler functions
- Update both local and global state
- Integrate with Socket.io for real-time updates
- Handle loading and error states consistently

### Socket Integration with Redux

```typescript
// ✅ Good - Socket event handlers with Redux
useEffect(() => {
  const onUpdateBoard = (board: BoardResType['result']) => {
    dispatch(updateActiveBoard(board))
  }

  const onUpdateCard = (card: CardType) => {
    dispatch(updateActiveCard(card))
    dispatch(updateCardInBoard(card))
  }

  socket?.on('SERVER_BOARD_UPDATED', onUpdateBoard)
  socket?.on('SERVER_CARD_UPDATED', onUpdateCard)

  return () => {
    socket?.off('SERVER_BOARD_UPDATED', onUpdateBoard)
    socket?.off('SERVER_CARD_UPDATED', onUpdateCard)
  }
}, [dispatch, socket])
```

## API State Management

### Cache Invalidation Patterns

```typescript
// ✅ Good - Smart cache invalidation
invalidatesTags: (_result, _error, { id }) => [
  { type: 'Board', id },        // Invalidate specific item
  { type: 'Board', id: 'LIST' }  // Invalidate list cache
]

// ✅ Good - Provide tags for caching
providesTags: (result) =>
  result
    ? [
        ...result.result.boards.map(({ _id }) => ({ type: 'Board' as const, id: _id })),
        { type: 'Board' as const, id: 'LIST' }
      ]
    : [{ type: 'Board' as const, id: 'LIST' }]
```

### API State Reset Pattern ([auth.ts](mdc:src/queries/auth.ts))

```typescript
// ✅ Good - API state reset on logout
export const apiSlicesToReset = [boardApi, userApi, invitationApi]

export const resetApiState = (dispatch: AppDispatch) => {
  apiSlicesToReset.forEach((api) => {
    dispatch(api.util.resetApiState())
  })
}

// Usage in logout mutation
logout: build.mutation<LogoutResType, void>({
  query: () => ({ url: `${AUTH_API_URL}/logout`, method: 'POST' }),
  async onQueryStarted(_args, { dispatch, queryFulfilled }) {
    try {
      await queryFulfilled
      dispatch(reset())
      dispatch(disconnectSocket())
      dispatch(authApi.util.resetApiState())
      resetApiState(dispatch)
    } catch (error: any) {
      console.error(error)
    }
  }
})
```

## Error Handling and User Feedback

### Comprehensive Error Handling

```typescript
// ✅ Good - Error handling with user feedback
const onUploadCardCoverPhoto = async (event: React.ChangeEvent<HTMLInputElement>) => {
  const file = event.target.files?.[0]

  const errorMessage = singleFileValidator(file as File)
  if (errorMessage) {
    toast.error(errorMessage, { position: 'top-center' })
    return
  }

  const formData = new FormData()
  formData.append('image', file as File)

  const uploadImageRes = await toast.promise(uploadImageMutation(formData).unwrap(), {
    pending: 'Uploading...',
    success: 'Upload successfully!',
    error: 'Upload failed!'
  })

  const imageUrl = uploadImageRes.result[0].url
  
  handleUpdateActiveCard({ cover_photo: imageUrl }).finally(() => {
    event.target.value = ''
  })
}
```

**Error Handling Best Practices:**
- Validate data before API calls
- Use toast notifications for user feedback
- Handle both client-side and server-side errors
- Clean up form inputs after processing
- Use toast.promise for async operation feedback

## Real-time State Synchronization

### Socket.io Integration Pattern

```typescript
// ✅ Good - Real-time state sync
useEffect(() => {
  if (boardId) {
    socket?.emit('CLIENT_JOIN_BOARD', boardId)
  }

  return () => {
    if (boardId) {
      socket?.emit('CLIENT_LEAVE_BOARD', boardId)
    }
  }
}, [boardId, socket])

// Emit updates to other clients
const handleUpdateActiveCard = async (body: UpdateCardBodyType) => {
  // ... update logic
  
  // Broadcast change to other users
  socket?.emit('CLIENT_USER_UPDATED_CARD', updatedCard)
  
  return updatedCard
}
```

## Performance Optimizations

### Selective State Updates

```typescript
// ✅ Good - Targeted state updates
updateCardInBoard: (state, action: PayloadAction<CardType>) => {
  const incomingCard = action.payload
  const column = state.activeBoard?.columns?.find((col) => col._id === incomingCard?.column_id)
  
  if (column) {
    const card = column.cards?.find((item) => item._id === incomingCard._id)
    if (card) {
      Object.keys(incomingCard).forEach((key) => {
        ;(card as Record<string, any>)[key] = incomingCard[key as keyof CardType]
      })
    }
  }
}
```

### Optimistic Updates

```typescript
// ✅ Good - Optimistic updates pattern
const onMoveColumns = (dndOrderedColumns: ColumnType[]) => {
  // Update UI immediately
  const newActiveBoard = { ...activeBoard! }
  newActiveBoard.columns = dndOrderedColumns
  newActiveBoard.column_order_ids = dndOrderedCardsIds
  dispatch(updateActiveBoard(newActiveBoard))

  // Persist to server
  updateBoardMutation({
    id: newActiveBoard._id,
    body: { column_order_ids: newActiveBoard.column_order_ids }
  })

  // Notify other clients
  socket?.emit('CLIENT_USER_UPDATED_BOARD', newActiveBoard)
}
```

## File Organization

### Query Files Structure
- Group by entity: `auth.ts`, `boards.ts`, `cards.ts`
- Export both hooks and API slice
- Use consistent naming conventions
- Import common dependencies at the top

### Slice Files Structure
- One slice per feature domain
- Include both sync and async actions
- Export actions and reducer separately
- Define proper TypeScript interfaces

## Common Anti-Patterns to Avoid

### Incorrect Hook Usage
```typescript
// ❌ Bad - Direct Redux hooks
import { useDispatch, useSelector } from 'react-redux'

// ✅ Good - Typed hooks
import { useAppDispatch, useAppSelector } from '~/lib/redux/hooks'
```

### Missing Error Handling
```typescript
// ❌ Bad - No error handling
const updateCard = async () => {
  await updateCardMutation(data).unwrap()
}

// ✅ Good - Proper error handling
const updateCard = async () => {
  try {
    await updateCardMutation(data).unwrap()
    toast.success('Updated successfully!')
  } catch (error) {
    toast.error('Update failed!')
    console.error(error)
  }
}
```

### Improper Cache Management
```typescript
// ❌ Bad - No cache invalidation
addBoard: build.mutation<BoardResType, CreateBoardBodyType>({
  query: (body) => ({ url: BOARD_API_URL, method: 'POST', data: body })
})

// ✅ Good - Proper cache invalidation
addBoard: build.mutation<BoardResType, CreateBoardBodyType>({
  query: (body) => ({ url: BOARD_API_URL, method: 'POST', data: body }),
  invalidatesTags: [{ type: 'Board', id: 'LIST' }]
})
```

These patterns ensure efficient state management, proper error handling, real-time synchronization, and excellent developer experience across the entire React application using Redux Toolkit and RTK Query.
