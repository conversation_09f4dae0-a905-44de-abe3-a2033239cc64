---
description: Material-UI styling best practices
globs:
alwaysApply: true
---

# Material-UI Styling Best Practices

This rule defines the comprehensive styling patterns used throughout the React + TypeScript application with Material-UI (MUI), **prioritizing the `sx` prop over the `styled` API**.

## Core Styling Philosophy

### 1. Sx Prop First Approach

**Always prefer the `sx` prop for styling MUI components.** The `sx` prop provides:

- Better performance (no additional component wrapper)
- Type safety with theme integration
- Cleaner component trees
- Direct access to theme tokens
- Better debugging experience

```typescript
// ✅ PREFERRED - Use sx prop for styling
<Box
  sx={{
    display: 'flex',
    alignItems: 'center',
    gap: 1.5,
    p: 2,
    borderRadius: 1,
    bgcolor: (theme) => (theme.palette.mode === 'dark' ? '#2f3542' : '#091e420f'),
    '&:hover': {
      bgcolor: (theme) => (theme.palette.mode === 'dark' ? '#33485D' : theme.palette.grey[300])
    }
  }}
>
  Content
</Box>

// ❌ AVOID - Unnecessary styled component for single use
const StyledBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1.5),
  padding: theme.spacing(2)
}))
```

### 2. When to Use Styled Components

Reserve the `styled` API **ONLY** for these specific cases:

#### A. Reusable Components Used 5+ Times

```typescript
// ✅ ACCEPTABLE - Reused across multiple components (like SidebarItem in ActiveCard.tsx)
const SidebarItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '6px',
  cursor: 'pointer',
  fontSize: '14px',
  fontWeight: '600',
  color: theme.palette.mode === 'dark' ? '#90caf9' : '#172b4d',
  backgroundColor: theme.palette.mode === 'dark' ? '#2f3542' : '#091e420f',
  padding: '10px',
  borderRadius: '4px',
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' ? '#33485D' : theme.palette.grey[300]
  }
}))
```

#### B. HTML Elements Without sx Prop Support

```typescript
// ✅ REQUIRED - Native HTML elements don't support sx prop
const VisuallyHiddenInput = styled('input')({
  display: 'none'
})

const ProductivityImage = styled('img')(({ theme }) => ({
  maxWidth: '100%',
  height: 'auto',
  borderRadius: '20px',
  boxShadow: theme.palette.mode === 'dark' ? '0 25px 50px rgba(0, 0, 0, 0.5)' : '0 25px 50px rgba(0, 0, 0, 0.15)'
}))
```

#### C. Complex Component Variants with shouldForwardProp

```typescript
// ✅ ACCEPTABLE - Complex prop filtering required
const Main = styled('main', {
  shouldForwardProp: (prop) => prop !== 'workspaceDrawerOpen' && prop !== 'boardDrawerOpen'
})<{ workspaceDrawerOpen?: boolean; boardDrawerOpen?: boolean }>(({ theme, workspaceDrawerOpen, boardDrawerOpen }) => ({
  flexGrow: 1,
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen
  }),
  marginLeft: `-${theme.trellone.workspaceDrawerWidth}`,
  ...(workspaceDrawerOpen && {
    marginLeft: 0
  })
}))
```

## Sx Prop Patterns and Best Practices

### 1. Theme Integration

Always use theme functions for dynamic styling:

```typescript
// ✅ EXCELLENT - Theme-aware styling with sx
<Box
  sx={{
    backgroundColor: (theme) => (theme.palette.mode === 'dark' ? '#1A2027' : '#fff'),
    color: 'text.primary',
    bgcolor: 'background.paper',
    borderColor: 'divider'
  }}
>

// ✅ GOOD - Using theme palette tokens
<Typography sx={{ color: 'primary.main', fontWeight: 600 }}>

// ❌ BAD - Hard-coded colors
<Box sx={{ backgroundColor: '#ffffff', color: '#000000' }}>
```

### 2. Responsive Design with Breakpoints

```typescript
// ✅ EXCELLENT - Responsive design with sx
<Toolbar
  sx={{
    flexDirection: { xs: 'column', md: 'row' },
    alignItems: { xs: 'flex-start', md: 'center' },
    gap: 2,
    p: { xs: 1, sm: 2 }
  }}
>

// ✅ GOOD - Conditional responsive display
<IconButton
  sx={{
    display: { xs: 'none', md: 'flex' },
    ...(workspaceDrawerOpen && { display: 'none' })
  }}
>
```

### 3. Interactive States and Animations

```typescript
// ✅ EXCELLENT - Hover states and transitions with sx
<Box
  sx={{
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    '&:hover': {
      transform: 'scale(1.02)',
      boxShadow: (theme) => theme.shadows[4]
    },
    '&:active': {
      transform: 'scale(0.98)'
    }
  }}
>

// ✅ GOOD - Complex pseudo-selectors
<Tab
  sx={{
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: '-100%',
      width: '100%',
      height: '100%',
      background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)',
      transition: 'left 0.6s cubic-bezier(0.4, 0, 0.2, 1)'
    },
    '&.Mui-selected::before': {
      left: '100%'
    }
  }}
>
```

### 4. Style Object Extraction for Reusability

Extract frequently used styles to constants:

```typescript
// ✅ EXCELLENT - Reusable style constants
const MODAL_STYLES = {
  position: 'relative',
  width: '100%',
  maxWidth: 900,
  bgcolor: 'white',
  boxShadow: 24,
  borderRadius: '8px',
  border: 'none',
  outline: 0,
  padding: '40px 20px 20px',
  margin: '50px auto',
  backgroundColor: (theme: any) => (theme.palette.mode === 'dark' ? '#1A2027' : '#fff')
} as const

const SIDEBAR_ITEM_BASE_STYLES = {
  display: 'flex',
  alignItems: 'center',
  gap: '6px',
  cursor: 'pointer',
  fontSize: '14px',
  fontWeight: '600',
  p: 1.25,
  borderRadius: 0.5
} as const

// Usage in components
<Box sx={MODAL_STYLES}>
<Box sx={{ ...SIDEBAR_ITEM_BASE_STYLES, bgcolor: 'primary.main' }}>
```

### 5. Conditional Styling Patterns

```typescript
// ✅ EXCELLENT - Conditional styling with sx
<Box
  sx={{
    ...baseStyles,
    ...(isActive && {
      color: 'primary.main',
      bgcolor: 'primary.50'
    }),
    ...(isDisabled && {
      opacity: 0.5,
      pointerEvents: 'none'
    })
  }}
>

// ✅ GOOD - Dynamic styling based on props
<Typography
  sx={{
    color: error ? 'error.main' : 'text.primary',
    fontWeight: variant === 'heading' ? 600 : 400
  }}
>
```

## Component-Specific Patterns

### 1. Modal and Dialog Styling

```typescript
// ✅ PREFERRED - Modal with sx prop
<Modal
  open={isOpen}
  onClose={onClose}
  sx={{ overflowY: 'auto' }}
>
  <Box
    sx={{
      position: 'relative',
      width: '100%',
      maxWidth: 900,
      bgcolor: 'background.paper',
      boxShadow: 24,
      borderRadius: 2,
      p: 3,
      m: '50px auto'
    }}
  >
```

### 2. Form Input Styling

```typescript
// ✅ PREFERRED - TextField with sx
<TextField
  sx={{
    bgcolor: (theme) => (theme.palette.mode === 'dark' ? '#22272b' : '#feff0026'),
    '& .MuiOutlinedInput-root': {
      '&:hover fieldset': {
        borderColor: 'primary.main'
      }
    }
  }}
  variant="outlined"
  size="small"
/>
```

### 3. Layout Components

```typescript
// ✅ PREFERRED - Layout with sx
<Container
  maxWidth="lg"
  sx={{
    py: { xs: 4, md: 8 },
    px: { xs: 2, sm: 3 }
  }}
>

<Grid
  container
  spacing={2}
  sx={{
    mb: 3,
    '& .MuiGrid-item': {
      display: 'flex',
      flexDirection: 'column'
    }
  }}
>
```

## Migration Guidelines

### Converting Styled Components to Sx Prop

#### Before (Styled Component):

```typescript
// ❌ OUTDATED - Single-use styled component
const ProductivityImage = styled('img')(({ theme }) => ({
  maxWidth: '100%',
  height: 'auto',
  borderRadius: '20px',
  boxShadow: theme.palette.mode === 'dark'
    ? '0 25px 50px rgba(0, 0, 0, 0.5)'
    : '0 25px 50px rgba(0, 0, 0, 0.15)',
  transition: 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)'
}))

// Usage
<ProductivityImage src={image} alt="description" />
```

#### After (Sx Prop):

```typescript
// ✅ PREFERRED - Using sx prop with Box wrapper
<Box
  component="img"
  src={image}
  alt="description"
  sx={{
    maxWidth: '100%',
    height: 'auto',
    borderRadius: '20px',
    boxShadow: (theme) => theme.palette.mode === 'dark'
      ? '0 25px 50px rgba(0, 0, 0, 0.5)'
      : '0 25px 50px rgba(0, 0, 0, 0.15)',
    transition: 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)'
  }}
/>
```

### Migration Checklist

1. **Identify styled components used ≤4 times** → Convert to sx prop
2. **Extract common styles** → Create style constants
3. **Update imports** → Remove styled imports where not needed
4. **Test theme switching** → Ensure dark/light mode works
5. **Verify responsive behavior** → Check breakpoint functionality

## Performance Considerations

### 1. Sx Prop Performance Benefits

```typescript
// ✅ BETTER PERFORMANCE - No additional wrapper component
<Typography sx={{ color: 'primary.main', fontWeight: 600 }}>
  Text content
</Typography>

// ❌ WORSE PERFORMANCE - Creates additional component wrapper
const StyledTypography = styled(Typography)({
  color: 'primary.main',
  fontWeight: 600
})
<StyledTypography>Text content</StyledTypography>
```

### 2. Style Object Memoization

```typescript
// ✅ GOOD - Memoize complex style objects
const complexStyles = useMemo(() => ({
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  animation: 'slideIn 0.3s ease-out',
  transform: isVisible ? 'translateY(0)' : 'translateY(-100%)'
}), [theme, isVisible])

<Box sx={complexStyles}>
```

## Common Anti-Patterns to Avoid

### 1. Overusing Styled Components

```typescript
// ❌ BAD - Unnecessary styled component for simple styling
const SimpleBox = styled(Box)({
  display: 'flex',
  alignItems: 'center'
})

// ✅ GOOD - Use sx prop directly
<Box sx={{ display: 'flex', alignItems: 'center' }}>
```

### 2. Hard-coded Values

```typescript
// ❌ BAD - Hard-coded colors and sizes
<Box sx={{ color: '#1976d2', padding: '8px' }}>

// ✅ GOOD - Theme-based values
<Box sx={{ color: 'primary.main', p: 1 }}>
```

### 3. Missing Responsive Design

```typescript
// ❌ BAD - No responsive considerations
<Box sx={{ width: 300, fontSize: '16px' }}>

// ✅ GOOD - Responsive design
<Box sx={{
  width: { xs: '100%', sm: 300 },
  fontSize: { xs: '14px', sm: '16px' }
}}>
```

### 4. Inconsistent Theme Usage

```typescript
// ❌ BAD - Mixed theme access patterns
<Box sx={{
  backgroundColor: '#f5f5f5',  // Hard-coded
  color: (theme) => theme.palette.text.primary,  // Theme function
  padding: 2  // Theme spacing
}}>

// ✅ GOOD - Consistent theme usage
<Box sx={{
  bgcolor: 'background.paper',  // Theme token
  color: 'text.primary',        // Theme token
  p: 2                          // Theme spacing
}}>
```

## File Organization and Imports

### Import Patterns

```typescript
// ✅ GOOD - Minimal imports when using sx
import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'

// ❌ AVOID - Unnecessary styled import for sx-only components
import { styled } from '@mui/material/styles'
import Box from '@mui/material/Box'
```

### Component Structure

```typescript
// ✅ PREFERRED - Component using sx prop
export default function MyComponent() {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        p: 3,
        bgcolor: 'background.paper',
        borderRadius: 2
      }}
    >
      <Typography sx={{ fontWeight: 600, color: 'primary.main' }}>
        Title
      </Typography>
      <Typography sx={{ color: 'text.secondary' }}>
        Content
      </Typography>
    </Box>
  )
}
```

## Accessibility and UX with Sx Prop

### 1. Focus States

```typescript
// ✅ EXCELLENT - Accessible focus styling
<Box
  tabIndex={0}
  sx={{
    '&:focus': {
      outline: '2px solid',
      outlineColor: 'primary.main',
      outlineOffset: 2
    },
    '&:focus-visible': {
      boxShadow: (theme) => `0 0 0 3px ${theme.palette.primary.main}25`
    }
  }}
>
```

### 2. Loading and Disabled States

```typescript
// ✅ EXCELLENT - Clear visual feedback
<Button
  sx={{
    transition: 'all 0.2s ease',
    ...(loading && {
      opacity: 0.6,
      pointerEvents: 'none'
    }),
    ...(disabled && {
      bgcolor: 'action.disabled',
      color: 'action.disabled'
    })
  }}
>
```

## Summary

1. **Default to `sx` prop** for all MUI component styling
2. **Use `styled` only when**:
   - Component is reused 5+ times across the codebase
   - Styling HTML elements without `sx` support
   - Complex prop filtering is required
3. **Extract common styles** to constants for reusability
4. **Always use theme tokens** instead of hard-coded values
5. **Implement responsive design** with breakpoint objects
6. **Migrate existing styled components** to `sx` prop where appropriate

This approach ensures better performance, cleaner code, and improved maintainability while preserving the flexibility of the styled API where it's truly needed.

These patterns ensure consistent, maintainable, and accessible styling across the entire Material-UI React application while supporting both light and dark themes seamlessly.
