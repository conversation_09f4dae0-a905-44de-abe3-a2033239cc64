export const getFaviconUrl = (url: string, size: number = 64) => {
  try {
    const domain = new URL(url).hostname
    return `https://www.google.com/s2/favicons?sz=${size}&domain_url=${domain}`
  } catch (error) {
    console.error('Invalid URL provided to getFaviconUrl:', error)
    return ''
  }
}

export const extractDomain = (url: string) => {
  try {
    return new URL(url).hostname
  } catch (error) {
    console.error('Invalid URL provided to extractDomain:', error)
    return ''
  }
}

export const getDomainFromUrl = (url: string) => {
  try {
    const domain = new URL(url).hostname
    return domain.replace('www.', '')
  } catch {
    return url
  }
}

export interface DetectedLink {
  url: string
  text: string
  startIndex: number
  endIndex: number
  type: 'markdown' | 'plain'
}

/**
 * Detects URLs in text content, including both plain URLs and markdown links
 * @param content - The text content to search for URLs
 * @returns Array of detected links with their positions and types
 */
export const detectLinksInContent = (content: string): DetectedLink[] => {
  const links: DetectedLink[] = []

  // Regex for markdown links: [text](url)
  const markdownLinkRegex = /\[([^\]]+)\]\(([^)]+)\)/g

  // Regex for plain URLs (http/https)
  const plainUrlRegex = /(https?:\/\/[^\s<>"{}|\\^`[\]]+)/g

  let match

  // Find markdown links first
  while ((match = markdownLinkRegex.exec(content)) !== null) {
    const [fullMatch, text, url] = match

    // Validate the URL
    if (isValidUrl(url)) {
      links.push({
        url: url.trim(),
        text: text.trim(),
        startIndex: match.index,
        endIndex: match.index + fullMatch.length,
        type: 'markdown'
      })
    }
  }

  // Find plain URLs, but exclude those already captured in markdown links
  while ((match = plainUrlRegex.exec(content)) !== null) {
    const [fullMatch] = match
    const url = fullMatch.trim()

    // Check if this URL is already part of a markdown link
    const isPartOfMarkdownLink = links.some(
      (link) =>
        link.type === 'markdown' && match.index >= link.startIndex && match.index + fullMatch.length <= link.endIndex
    )

    if (!isPartOfMarkdownLink && isValidUrl(url)) {
      links.push({
        url,
        text: url,
        startIndex: match.index,
        endIndex: match.index + fullMatch.length,
        type: 'plain'
      })
    }
  }

  // Sort by start index to maintain order
  return links.sort((a, b) => a.startIndex - b.startIndex)
}

/**
 * Validates if a string is a valid URL
 * @param url - The URL string to validate
 * @returns True if the URL is valid, false otherwise
 */
export const isValidUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

/**
 * Extracts the first URL from markdown or plain text content
 * @param content - The content to extract URL from
 * @returns The first valid URL found, or null if none found
 */
export const extractFirstUrl = (content: string): string | null => {
  const links = detectLinksInContent(content)
  return links.length > 0 ? links[0].url : null
}
