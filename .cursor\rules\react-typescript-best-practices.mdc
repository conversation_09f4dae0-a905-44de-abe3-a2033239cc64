---
description: React + TypeScript best practices
globs:
alwaysApply: true
---
# React + TypeScript Best Practices

This rule defines the coding standards and patterns used throughout our React + TypeScript application.

## Import Organization

### Import Order (Always follow this exact order)

1. **External Library Icons** (Material-UI icons, sorted alphabetically)
2. **External Library Components** (Material-UI components, sorted alphabetically) 
3. **External Library Utilities** (styled, hooks, etc.)
4. **React Hooks and Utilities**
5. **Third-party Libraries** (lodash, react-toastify, etc.)
6. **Internal Components** (starting with `~/components/`)
7. **Internal Constants and Types** (starting with `~/constants/`, `~/types/`)
8. **Internal Hooks** (starting with `~/hooks/`, `~/lib/`)
9. **Internal Queries** (starting with `~/queries/`)
10. **Internal Schemas** (starting with `~/schemas/`)
11. **Internal Store/Redux** (starting with `~/store/`)
12. **Internal Utilities** (starting with `~/utils/`)

```typescript
// ✅ Good - Follow this exact import order
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import ArchiveOutlinedIcon from '@mui/icons-material/ArchiveOutlined'
import AttachmentIcon from '@mui/icons-material/Attachment'
import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import Modal from '@mui/material/Modal'
import { styled } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import { useMediaQuery, useTheme } from '@mui/material'
import { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import cloneDeep from 'lodash/cloneDeep'
import DrawerHeader from '~/components/DrawerHeader'
import NavBar from '~/components/NavBar'
import { CardMemberAction } from '~/constants/type'
import { useAppDispatch, useAppSelector } from '~/lib/redux/hooks'
import { useUpdateCardMutation } from '~/queries/cards'
import { CardAttachmentPayloadType } from '~/schemas/card.schema'
import { updateActiveCard } from '~/store/slices/card.slice'
import { singleFileValidator } from '~/utils/validators'

// ❌ Bad - Mixed import order
import { useEffect } from 'react'
import Box from '@mui/material/Box'
import { useAppDispatch } from '~/lib/redux/hooks'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
```

### Import Aliasing
- Always use `~` path alias for internal imports
- Use absolute imports for all internal modules
- Group related imports with line breaks between categories

## Component Structure

### File Organization
- Each component gets its own folder with `ComponentName.tsx` and `index.ts`
- Index files should re-export the default component
- Use PascalCase for component files and folders

```typescript
// ✅ Good - Component folder structure
components/Modal/ActiveCard/
├── ActiveCard.tsx
├── index.ts
└── subcomponents/

// ✅ Good - index.ts pattern
import ActiveCard from '~/components/Modal/ActiveCard/ActiveCard'
export default ActiveCard
```

### Component Definition
- Always use `export default function ComponentName()` syntax
- Use TypeScript interfaces for props (suffix with `Props`)
- Place component logic before JSX return

```typescript
// ✅ Good - Component structure
interface ComponentNameProps {
  prop1: string
  prop2?: boolean
  onAction: (value: string) => void
}

export default function ComponentName({ prop1, prop2, onAction }: ComponentNameProps) {
  // All hooks at the top
  const theme = useTheme()
  const dispatch = useAppDispatch()
  const [state, setState] = useState<string>('')
  
  // All mutation hooks
  const [updateMutation] = useUpdateMutation()
  
  // All handler functions
  const handleAction = async (value: string) => {
    // Implementation
  }
  
  // All useEffect hooks at the end of logic section
  useEffect(() => {
    // Side effects
  }, [dependency])
  
  // Early returns for loading/error states
  if (loading) {
    return <PageLoadingSpinner caption="Loading..." />
  }
  
  if (error) {
    return <ErrorComponent />
  }
  
  // Main JSX return
  return (
    <Box>
      {/* Component content */}
    </Box>
  )
}
```

## State Management Patterns

### Redux Usage
- Use typed hooks: `useAppDispatch` and `useAppSelector`
- Always destructure state selectors clearly
- Dispatch actions with descriptive names

```typescript
// ✅ Good - Redux patterns
const dispatch = useAppDispatch()
const { activeBoard, loading, error } = useAppSelector((state) => state.board)
const { profile } = useAppSelector((state) => state.auth)
const { socket } = useAppSelector((state) => state.app)

// ✅ Good - Action dispatch
dispatch(updateActiveCard(updatedCard))
dispatch(clearAndHideActiveCardModal())
```

### React Query Mutations
- Use array destructuring for mutations
- Name mutations descriptively with `Mutation` suffix

```typescript
// ✅ Good - Mutation hooks
const [updateCardMutation] = useUpdateCardMutation()
const [uploadImageMutation] = useUploadImageMutation()
const [moveCardToDifferentColumnMutation] = useMoveCardToDifferentColumnMutation()
```

## Styling Patterns

### Material-UI Styling
- Use `sx` prop for component-specific styles
- Use `styled` components for reusable styled elements
- Support both light and dark themes in styled components

```typescript
// ✅ Good - Styled component with theme support
const SidebarItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '6px',
  cursor: 'pointer',
  fontSize: '14px',
  fontWeight: '600',
  color: theme.palette.mode === 'dark' ? '#90caf9' : '#172b4d',
  backgroundColor: theme.palette.mode === 'dark' ? '#2f3542' : '#091e420f',
  padding: '10px',
  borderRadius: '4px',
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' ? '#33485D' : theme.palette.grey[300],
  }
}))

// ✅ Good - Inline sx prop usage
<Box
  sx={{
    position: 'relative',
    width: '100%',
    maxWidth: 900,
    bgcolor: 'white',
    backgroundColor: (theme) => (theme.palette.mode === 'dark' ? '#1A2027' : '#fff')
  }}
>
```

### Layout Patterns
- Use Material-UI Grid system (Grid2) for responsive layouts
- Use Stack for simple vertical/horizontal layouts
- Use Box for container elements with custom styling

```typescript
// ✅ Good - Grid layout
<Grid container spacing={2}>
  <Grid xs={12} sm={9}>
    {/* Main content */}
  </Grid>
  <Grid xs={12} sm={3}>
    {/* Sidebar */}
  </Grid>
</Grid>

// ✅ Good - Stack layout
<Stack direction="column" spacing={1}>
  <SidebarItem>Item 1</SidebarItem>
  <SidebarItem>Item 2</SidebarItem>
</Stack>
```

## Event Handling

### Handler Function Naming
- Prefix event handlers with `handle` or `on`
- Use descriptive names that indicate the action
- Make handlers async when they perform API calls

```typescript
// ✅ Good - Handler naming and structure
const handleActiveCardModalClose = () => {
  dispatch(clearAndHideActiveCardModal())
}

const onUpdateCardTitle = async (title: string) => {
  handleUpdateActiveCard({ title })
}

const onUploadCardCoverPhoto = async (event: React.ChangeEvent<HTMLInputElement>) => {
  const file = event.target.files?.[0]
  // Implementation with proper error handling
}
```

### Generic Update Handlers
- Create reusable update functions that handle common patterns
- Emit socket events for real-time updates
- Update both local state and global state

```typescript
// ✅ Good - Generic update handler pattern
const handleUpdateActiveCard = async (body: UpdateCardBodyType) => {
  const updateCardRes = await updateCardMutation({ 
    id: activeCard?._id as string, 
    body 
  }).unwrap()

  const updatedCard = updateCardRes.result

  // Update local state
  dispatch(updateActiveCard(updatedCard))
  
  // Update global state
  dispatch(updateCardInBoard(updatedCard))

  // Emit socket event for real-time updates
  socket?.emit('CLIENT_USER_UPDATED_CARD', updatedCard)

  return updatedCard
}
```

## Socket Integration

### Socket Event Patterns
- Use descriptive event names with CLIENT/SERVER prefixes
- Clean up socket listeners in useEffect cleanup
- Handle connect/disconnect events for debugging

```typescript
// ✅ Good - Socket integration pattern
useEffect(() => {
  if (boardId) {
    socket?.emit('CLIENT_JOIN_BOARD', boardId)
  }

  return () => {
    if (boardId) {
      socket?.emit('CLIENT_LEAVE_BOARD', boardId)
    }
  }
}, [boardId, socket])

useEffect(() => {
  const onUpdateBoard = (board: BoardResType['result']) => {
    dispatch(updateActiveBoard(board))
  }

  socket?.on('SERVER_BOARD_UPDATED', onUpdateBoard)

  return () => {
    socket?.off('SERVER_BOARD_UPDATED', onUpdateBoard)
  }
}, [dispatch, socket])
```

## Error Handling and Validation

### File Upload Validation
- Use utility functions for validation
- Provide user-friendly error messages with toast notifications
- Reset form inputs after processing

```typescript
// ✅ Good - File upload with validation
const onUploadCardCoverPhoto = async (event: React.ChangeEvent<HTMLInputElement>) => {
  const file = event.target.files?.[0]

  const errorMessage = singleFileValidator(file as File)

  if (errorMessage) {
    toast.error(errorMessage, { position: 'top-center' })
    return
  }

  // Process file upload with toast promise
  const uploadImageRes = await toast.promise(uploadImageMutation(formData).unwrap(), {
    pending: 'Uploading...',
    success: 'Upload successfully!',
    error: 'Upload failed!'
  })

  // Clean up
  event.target.value = ''
}
```

### Loading and Error States
- Show loading spinners with descriptive captions
- Create dedicated error components
- Use early returns for better code readability

```typescript
// ✅ Good - Loading and error handling
if (loading === 'pending') {
  return <PageLoadingSpinner caption="Loading board..." />
}

if (error || !activeBoard) {
  return <BoardNotFound />
}
```

## Naming Conventions

### Variables and Functions
- Use camelCase for variables and functions
- Use descriptive names that indicate purpose
- Use TypeScript types for better IntelliSense

```typescript
// ✅ Good - Naming conventions
const isDarkMode = theme.palette.mode === 'dark'
const isScreenBelowMedium = useMediaQuery(theme.breakpoints.down('md'))
const dndOrderedColumns = columns.map(...)
const attachmentButtonRef = useRef<HTMLButtonElement | null>(null)
```

### Constants and Types
- Use PascalCase for types and interfaces
- Use UPPER_SNAKE_CASE for constants
- Use descriptive suffixes like `Type`, `Props`, `Payload`

```typescript
// ✅ Good - Type and constant naming
interface CardAttachmentPayloadType {
  // properties
}

enum CardMemberAction {
  Add = 'ADD',
  Remove = 'REMOVE'
}

const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB
```

## Conditional Rendering

### Safe Property Access
- Use optional chaining (`?.`) for nested properties
- Use logical AND (`&&`) for conditional rendering
- Use double negation (`!!`) for array length checks

```typescript
// ✅ Good - Conditional rendering patterns
{activeCard?.cover_photo && (
  <Box>Cover photo content</Box>
)}

{activeCard?.attachments && !!activeCard.attachments.length && (
  <CardAttachments />
)}

{activeCard?.members?.includes(profile?._id as string) ? (
  <RemoveButton />
) : (
  <JoinButton />
)}
```

## Performance Optimizations

### Component Optimization
- Use cloneDeep for complex state updates
- Memoize expensive calculations
- Clean up resources in useEffect cleanup functions

```typescript
// ✅ Good - Performance patterns
const newActiveBoard = cloneDeep(activeBoard)

useEffect(() => {
  // Setup
  return () => {
    // Cleanup
    dispatch(clearActiveBoard())
  }
}, [])
```

## Accessibility and UX

### User Interaction Feedback
- Use toast notifications for user actions
- Provide loading states for async operations
- Use semantic HTML elements and ARIA labels where appropriate

```typescript
// ✅ Good - User feedback
const uploadImageRes = await toast.promise(uploadImageMutation(formData).unwrap(), {
  pending: 'Uploading...',
  success: 'Upload successfully!',
  error: 'Upload failed!'
})
```

## Component Composition

### Props Interface Design
- Use clear, descriptive prop names
- Make optional props explicit with `?`
- Use callback props for parent-child communication

```typescript
// ✅ Good - Props interface
interface CardUserGroupProps {
  cardMembers: string[]
  onUpdateCardMembers: (member: CardMemberPayloadType) => void
}
```

These patterns ensure consistency, maintainability, and excellent developer experience across the entire React + TypeScript codebase.
