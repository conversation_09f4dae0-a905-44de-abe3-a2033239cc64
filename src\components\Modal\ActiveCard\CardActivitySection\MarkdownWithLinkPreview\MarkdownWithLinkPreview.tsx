import { useColorScheme } from '@mui/material'
import Box from '@mui/material/Box'
import MDEditor from '@uiw/react-md-editor'
import { useCallback, useEffect, useRef, useState } from 'react'
import LinkPreviewPopup, { LinkPreviewData } from '~/components/Modal/ActiveCard/CardActivitySection/LinkPreviewPopup'
import { isValidUrl } from '~/utils/url'

interface MarkdownWithLinkPreviewProps {
  source: string
  style?: React.CSSProperties
}

export default function MarkdownWithLinkPreview({ source, style }: MarkdownWithLinkPreviewProps) {
  const { mode } = useColorScheme()
  const containerRef = useRef<HTMLDivElement>(null)
  const showTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [linkPreviewAnchor, setLinkPreviewAnchor] = useState<HTMLElement | null>(null)
  const [linkPreviewData, setLinkPreviewData] = useState<LinkPreviewData | null>(null)

  const showPreview = useCallback((target: HTMLElement, url: string) => {
    setLinkPreviewAnchor(target)
    setLinkPreviewData({
      url,
      title: '',
      description: '',
      domain: ''
    })
  }, [])

  const hidePreview = useCallback(() => {
    setLinkPreviewAnchor(null)
    setLinkPreviewData(null)
  }, [])

  const handleLinkMouseEnter = useCallback(
    (event: MouseEvent, url: string) => {
      // Clear any existing hide timeout
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
        hideTimeoutRef.current = null
      }

      // Clear any existing show timeout
      if (showTimeoutRef.current) {
        clearTimeout(showTimeoutRef.current)
      }

      // Set a delay before showing the preview
      showTimeoutRef.current = setTimeout(() => {
        const target = event.target as HTMLElement
        showPreview(target, url)
      }, 500) // 500ms delay as specified in requirements
    },
    [showPreview]
  )

  const handleLinkMouseLeave = useCallback(() => {
    // Clear the show timeout if mouse leaves before delay completes
    if (showTimeoutRef.current) {
      clearTimeout(showTimeoutRef.current)
      showTimeoutRef.current = null
    }

    // Add a small delay before hiding to allow mouse to move to popup
    hideTimeoutRef.current = setTimeout(() => {
      hidePreview()
    }, 150) // Small delay to allow transition to popup
  }, [hidePreview])

  const handlePreviewMouseEnter = useCallback(() => {
    // Clear any pending hide timeout when entering popup
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
      hideTimeoutRef.current = null
    }
  }, [])

  const handlePreviewMouseLeave = useCallback(() => {
    // Hide the preview when leaving the popup
    hidePreview()
  }, [hidePreview])

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    // Function to add event listeners to all links
    const addLinkEventListeners = () => {
      const links = container.querySelectorAll('a[href]')

      links.forEach((link) => {
        const href = link.getAttribute('href')
        if (!href || !isValidUrl(href)) return

        const handleMouseEnter = (event: Event) => {
          handleLinkMouseEnter(event as MouseEvent, href)
        }

        const handleMouseLeave = () => {
          handleLinkMouseLeave()
        }

        // Remove existing listeners to avoid duplicates
        link.removeEventListener('mouseenter', handleMouseEnter)
        link.removeEventListener('mouseleave', handleMouseLeave)

        // Add new listeners
        link.addEventListener('mouseenter', handleMouseEnter)
        link.addEventListener('mouseleave', handleMouseLeave)
      })
    }

    // Add listeners initially
    addLinkEventListeners()

    // Use MutationObserver to detect when content changes and re-add listeners
    const observer = new MutationObserver(() => {
      addLinkEventListeners()
    })

    observer.observe(container, {
      childList: true,
      subtree: true
    })

    // Cleanup function
    return () => {
      observer.disconnect()
      if (showTimeoutRef.current) {
        clearTimeout(showTimeoutRef.current)
      }
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
      }

      // Remove all event listeners
      const links = container.querySelectorAll('a[href]')
      links.forEach((link) => {
        const href = link.getAttribute('href')
        if (!href || !isValidUrl(href)) return

        const handleMouseEnter = (event: Event) => {
          handleLinkMouseEnter(event as MouseEvent, href)
        }

        const handleMouseLeave = () => {
          handleLinkMouseLeave()
        }

        link.removeEventListener('mouseenter', handleMouseEnter)
        link.removeEventListener('mouseleave', handleMouseLeave)
      })
    }
  }, [source, handleLinkMouseEnter, handleLinkMouseLeave]) // Re-run when source or handlers change

  return (
    <>
      <Box ref={containerRef} data-color-mode={mode}>
        <MDEditor.Markdown
          source={source}
          style={{
            whiteSpace: 'pre-wrap',
            padding: '0px',
            border: 'none',
            borderRadius: '0px',
            backgroundColor: 'transparent',
            ...style
          }}
        />
      </Box>

      <LinkPreviewPopup
        open={Boolean(linkPreviewAnchor && linkPreviewData)}
        anchorEl={linkPreviewAnchor}
        linkData={linkPreviewData}
        onMouseEnter={handlePreviewMouseEnter}
        onMouseLeave={handlePreviewMouseLeave}
      />
    </>
  )
}
